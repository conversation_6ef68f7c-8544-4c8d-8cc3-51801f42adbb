<script setup lang="ts"></script>

<template>
  <ALayout class="h-screen max-h-screen bg-transparent!">
    <AppSider />

    <ALayoutContent class="h-screen! overflow-auto!">
      <RouterView v-slot="{ Component }">
        <Transition
          mode="out-in"
          enter-active-class="animate-in fade-in ease-out"
          leave-active-class="animate-out fade-out ease-out"
        >
          <Suspense>
            <component :is="Component" />
          </Suspense>
        </Transition>
      </RouterView>
    </ALayoutContent>
  </ALayout>
</template>
