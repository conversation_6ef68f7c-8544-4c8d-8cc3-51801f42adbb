<template>
    <div>
        <div class="container">
            <div class="flex flex-s-between">
                <div>
                    <el-button type="primary" @click="addQuestion">添加题目</el-button>
                    <el-button type="primary" @click="excelAddQuestion">批量导入</el-button>
                    <el-button type="primary" @click="addAiQuestion">AI生成题目</el-button>
                    <uploadFile></uploadFile>
                </div>
                <div>
                    <el-input
                        v-model="keyword"
                        clearable
                        style="width: 240px;margin-right: 20px;"
                        placeholder="搜索"
                        @clear="getQuestionList"
                    >
                        <template #suffix>
                            <el-icon @click="getQuestionList" class="search-icon">
                                <Search />
                            </el-icon>
                        </template>
                    </el-input>
                    <el-tree-select v-model="questionType" :data="data" style="width:140px">
                        <template #default="{ data: { label } }">
                        {{ label }}
                        </template>
                    </el-tree-select>
                </div>
            </div>

            <div class="table-header">
                <div class="cell-1-0">
                    <el-checkbox size="large" @click="handleSelectAll" v-model="selectAll" />
                </div>
                <div class="cell-1-1 com-cell">序号</div>
                <div class="cell-1-2 com-cell">题干</div>
                <!-- <div class="cell-1-3 com-cell">知识点</div> -->
                <div class="cell-1-4 com-cell">标签</div>
                <div class="cell-1-5 com-cell">题型</div>
                <div class="cell-1-6 com-cell">操作</div>
            </div>
            <div v-loading="loading">
                <div v-for="(question, index) in examList" :key="question.id" class="tbodyTr" :class="{ 'tbodyTr-1': index % 2 !== 0&&checkQuestionId != question.id }">
                    <div class="flex">
                        <div class="cell-1-0">
                            <el-checkbox size="large" v-model="question.isSelected" />
                        </div>
                        <div class="cell-1-1 com-cell-d">{{ index + 1 }}</div>
                        <div class="cell-1-2 com-cell-d"  v-html="parseMarkdown(question.text)"></div>
                        <!-- <div class="cell-1-3 com-cell-d">{{ question.knowledge_point }}</div> -->
                        <div class="cell-1-4 com-cell-d">{{ question.label_id }}</div>
                        <div class="cell-1-5 com-cell-d">{{ question.type  }}</div>
                        <div class="cell-1-6 com-cell-d">
                            <el-button round style="border: none;background: none;color: #409eff;" @click="questionDetails(question)">
                                {{ checkQuestionId == question.id ? '收起' : '详情'}}
                            </el-button>
                        </div>
                    </div>
                    <transition name="slide-fade">
                        <div class="detailsQuestion" v-show="checkQuestionId == question.id">
                            <div style="margin: 20px 0;justify-content: space-between;" class="flex">
                                <div>
                                    <el-button type="primary" size="large" :icon="Edit" @click="editQuestion(question)">编辑题目</el-button>
                                    <el-button type="primary" size="large" :icon="Delete" :loading="loadingDel" plain @click="deleteQuestion(question)">删除题目</el-button>
                                </div>
                                <!-- <el-button type="primary" size="large" >查看试题详细信息</el-button> -->
                            </div>
                            <div>
                                <div class="detailsQ-title">查看试题详细信息</div>
                                <questionDetailsCom :questionItem="question"></questionDetailsCom>
                            </div>
                        </div>
                    </transition>
                </div>
            </div>

            <template v-if="examList.length === 0">
                <el-empty description="暂无数据" />
            </template>
            <div class="flex flex-s-between" style="margin-top: 20px;">
                <el-button type="primary" @click="deleteSelectedQuestions" :loading="loadingDelAll">删除题目</el-button>
                <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[10, 30, 50, 100]"
                    :size="size"
                    :disabled="disabled"
                    :background="background"
                    layout="sizes, prev, pager, next"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>



    </div>
</template>
<script lang="ts" setup name="List">
import { Delete, Edit, Search, Share, Upload } from '@element-plus/icons-vue'
import { reactive, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'

import { parseMarkdown } from '@/utils/markdownParser'; // 引入工具函数
import { questionList, deleteQuestions, deleteQuestionsBatch, excelAddQue } from '@/services/api/question'
import { openMessage, openMessageBox, getEn, levelTransform, questionTypeData} from "@/utils/util";
import type { ComponentSize } from 'element-plus'
import { usePagination } from '@/hooks/usePagination';


import uploadFile from '@/components/ui/upload.vue'
import questionDetailsCom from '@/components/question/questionDetails.vue'

// import { computed } from 'vue';

// const isAnswerObject = computed((answer) => {
//     return Object.prototype.toString.call(answer) === '[object Object]';
// });
const questionType = ref('所有题型')
const data = questionTypeData


const size = ref<ComponentSize>('default')
const background = ref(false)
const disabled = ref(false)

const loadingDel = ref(false)
const loadingDelAll = ref(false)
const selectAll = ref(false)

const keyword = ref('') //搜索关键字

interface QuestionOption {
  value: string;
  key: string;
}
interface Question {
    id: number
    type: string
    text: string
    answer: any
    knowledge_point: string
    difficulty: string,
    explanation: string,
    label_id: number[]
    options: QuestionOption[];
    isSelected: boolean; // 新增字段
}

const loading = ref(false)
const router = useRouter()
const examList = reactive<Question[]>([]) //题目列表
const checkQuestionId = ref(0) //当前选中题目id

const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getQuestionList);

onMounted(() => {
    getQuestionList()
})

watch(questionType, (newValue) => {
    getQuestionList()
})

//获取题目列表
async function getQuestionList() {
    loading.value = true
    const params = {
        page: currentPage.value,
        pageSize: pageSize.value,
        keyword: keyword.value,
        type: questionType.value == '所有题型' ? '' : questionType.value
    }
    try {
        const res = await questionList(params) // 调用 API
        if (res && res.data) { // 检查是否有 results 字段
            examList.splice(0, examList.length) // 清空数组
            examList.push(
                ...res.data.results.map((item: any) => ({
                    ...item,
                    isSelected: false // 默认未选中
                }))
            ) // 使用扩展运算符正确赋值
            total.value = res.data.count
            loading.value = false
        }
    } catch (error) {
        console.error('获取题目列表失败:', error)
    }
}

//批量删除
function deleteSelectedQuestions() {
    const selectedIds = examList.filter((question) => question.isSelected).map((question) => question.id);
    if (selectedIds.length === 0) {
        openMessage('请至少选择一项进行删除', 'warning');
        return;
    }
    // console.log(selectedIds,'selectedIds')

    openMessageBox('确认要删除选中的全部题目吗？', '提示').then((result) => {
        if (result === 'confirmed') {
            loadingDelAll.value = true;
            deleteQuestionsBatch({ ids: selectedIds }).then(() => {
                loadingDelAll.value = false;
                selectAll.value = false;
                openMessage('删除成功', 'success');
                getQuestionList(); // 刷新列表
            });
        }
    });
}

function handleSelectAll() {
    examList.forEach((question) => question.isSelected = !selectAll.value);
}


function questionDetails(params: Question) {
    if (checkQuestionId.value == params.id) {
        checkQuestionId.value = 0
    } else {
        checkQuestionId.value = params.id
    }
}

//跳转新增题目页面
function addQuestion() {
    router.push({
        name: 'QUESTIONADD',
        query: { qid: '',type: '' }
    })
}
function addAiQuestion() {
    router.push({
        name: 'QUESTIONAIADD',
    })
}
// 编辑题目
function editQuestion(question: Question) {
    // console.log(question,'ididi')
    router.push({
        name: 'QUESTIONADD',
        query: { qid:question.id, type: question.type }
    })
}

// 删除题目
function deleteQuestion(question: Question) {
    openMessageBox('确认要删除吗？', '提示').then((result) => {
        if (result === 'confirmed') {
            loadingDel.value = true
            deleteQuestions({ id:question.id }).then(res => {
                loadingDel.value = false
                openMessage('删除成功', 'success')
                getQuestionList()
            })

        }
    })
}


//批量导入
function excelAddQuestion() {
    // const params ={
    //     file: ''
    // }
    // excelAddQue(params).then(res => {

    // })
}



</script>
<style lang="scss" scoped>
@import url('@/assets/css/questioncss/style.css');
@import url('@/assets/css/questioncss/questionList.css');
</style>
