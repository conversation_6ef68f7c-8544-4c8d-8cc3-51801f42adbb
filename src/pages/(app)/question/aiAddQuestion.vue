<template>
  <div class="flex">
    <div class="left">
      <div>AI出题</div>
      <div>出题范围</div>
      <div>
        <el-input
          v-model="params.content"
          :rows="10"
          type="textarea"
          placeholder="请输入出题范围"
        />
      </div>
      <div style="height: 20%"></div>
      <div>出题设置</div>
      <div style="margin: 20px 0">
        <el-select
          v-model="params.difficulty"
          placeholder="请选择试题难度"
          size="large"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div>
        <el-popover
          placement="bottom"
          width="20%"
          trigger="click"
          ref="popoverRef"
          :visible="showSelect"
        >
          <template #reference>
            <div
              class="selectType"
              @click="selectClick"
              :class="{ selectTypeActive: showSelect }"
            >
              <div v-if="dynamicTags.length > 0">
                <el-tag
                  v-for="tag in dynamicTags"
                  :key="tag"
                  closable
                  :disable-transitions="true"
                  @close="handleClose(tag)"
                  style="margin: 3px"
                >
                  {{ tag }}
                </el-tag>
              </div>
              <div
                v-else
                style="color: #606266; padding: 0px 8px"
              >
                请选择
              </div>
              <div style="margin-right: 10px">
                <el-icon
                  color="#a8abb2"
                  v-if="showSelect"
                >
                  <ArrowUp
                /></el-icon>
                <el-icon
                  color="#a8abb2"
                  v-else
                  ><ArrowDown
                /></el-icon>
              </div>
            </div>
          </template>

          <div
            class="popoverflex1"
            v-for="item in options1"
            :key="item.num"
            @click="selectTypeClick(item.name, item.num)"
          >
            <div :class="{ bold: dynamicTags.indexOf(item.name + '(' + item.num + ')') != -1 }">
              {{ item.name }}
            </div>
            <el-input-number
              v-model="item.num"
              :min="0"
              :max="100"
              @change="handleChange(item.name, item.num)"
            />
          </div>
        </el-popover>
      </div>

      <div class="button">
        <el-button
          type="primary"
          round
          @click="addAiQuestion"
          >生成试题</el-button
        >
      </div>
    </div>
    <div class="right">
      <div class="flexrighttop">
        <div>出题结果</div>
        <div>
          <el-button
            type="primary"
            round
            plain
            @click="clearQuestion"
            >一键清空</el-button
          >
          <el-button
            type="primary"
            round
            @click="addQuestionBank"
            >加入题库</el-button
          >
        </div>
      </div>
      <div
        class="loading"
        v-if="loading"
      >
        <Loading></Loading>
      </div>
      <div v-else>
        <div v-if="aiQuestion.length == 0">暂无数据</div>
        <div v-for="item in aiQuestion">
          <!-- :type="" -->
          <questionDetailsCom :questionItem="item"></questionDetailsCom>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue'

  import questionDetailsCom from '@/components/question/questionDetails.vue'
  import { aiQuestions, batchCreate } from '@/services/api/question'
  import Loading from '@/components/ui/loading.vue'

  import { openMessage } from '@/utils/util'

  const params = ref({
    content: '',
    difficulty: 0,
    single_question_count: 0, //单选题数量
    multiple_question_count: 0, //多选题数量
    boolean_question_count: 0, //判断题数量
    faq_question_count: 0, //简答题数量
    blank_filling_question_count: 0, //填空题数量
    subject: '', //科目
  })

  const aiQuestion = ref([])
  const loading = ref(false)

  const handleChange = (value: string, num: number) => {
    const tag = value + '(' + num + ')'
    const index = dynamicTags.value.findIndex((item) => item.includes(value))

    if (num === 0) {
      if (index !== -1) {
        dynamicTags.value.splice(index, 1)
      }
    } else {
      if (index !== -1) {
        dynamicTags.value.splice(index, 1, tag)
      } else {
        dynamicTags.value.push(tag)
      }
    }
  }
  const options = [
    {
      value: 0,
      label: '简单',
    },
    {
      value: 1,
      label: '中等',
    },
    {
      value: 2,
      label: '困难',
    },
  ]

  const options1 = ref([
    {
      name: '单选题',
      num: 1,
    },
    {
      name: '多选题',
      num: 1,
    },
    {
      name: '判断题',
      num: 1,
    },
    {
      name: '填空题',
      num: 1,
    },
    {
      name: '简答题',
      num: 1,
    },
  ])

  const showSelect = ref(false)
  const popoverRef = ref(null)
  const dynamicTags = ref<string[]>(['单选题(1)'])
  const handleClose = async (tag: any) => {
    dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1)
  }

  function selectClick() {
    showSelect.value = !showSelect.value
  }
  function selectTypeClick(type: string, num: number) {
    const tag = type + '(' + num + ')'
    const index = dynamicTags.value.indexOf(tag)

    if (index === -1) {
      dynamicTags.value.push(tag)
    } else {
      dynamicTags.value.splice(index, 1)
    }
  }

  function addAiQuestion() {
    loading.value = true
    showSelect.value = false
    dynamicTags.value.forEach((item) => {
      if (item.includes('单选题')) {
        params.value.single_question_count = Number(item.split('(')[1].split(')')[0])
      } else if (item.includes('多选题')) {
        params.value.multiple_question_count = Number(item.split('(')[1].split(')')[0])
      } else if (item.includes('判断题')) {
        params.value.boolean_question_count = Number(item.split('(')[1].split(')')[0])
      } else if (item.includes('填空题')) {
        params.value.blank_filling_question_count = Number(item.split('(')[1].split(')')[0])
      } else if (item.includes('简答题')) {
        params.value.faq_question_count = Number(item.split('(')[1].split(')')[0])
      }
    })
    aiQuestions(params.value)
      .then((res: any) => {
        aiQuestion.value = res.data
        loading.value = false
      })
      .finally(() => {
        loading.value = false
      })
  }

  //加入题库
  function addQuestionBank() {
    console.log('加入题库', aiQuestion.value)
    const params = {
      data: aiQuestion.value,
    }
    batchCreate(params).then((res: any) => {
      console.log(res, 'res')
      if (res.code === 200) {
        openMessage(res.message, 'success')
        aiQuestion.value = []
      }
    })
  }

  function clearQuestion() {
    aiQuestion.value = []
  }
</script>
<style lang="scss" scoped>
  @import url('@/assets/css/questioncss/aiaddQCss.css');
</style>
