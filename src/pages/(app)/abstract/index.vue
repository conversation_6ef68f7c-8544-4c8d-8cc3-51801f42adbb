<template>
  <div class="page">
      <div class="toolbar">
        <div class="toolbar-text">
            <span>摘要提取</span>
        </div>
        <div class="toolbar-buttons">
            <a-button class="toolbar-button_1">网状图</a-button>
            <a-button class="toolbar-button_1">树状图</a-button>
            <a-button class="toolbar-button_2">保存</a-button>
        </div>
      </div>

      <div class="box-container">
        <div class="box" style="  flex: 7;">

        </div>
        <div class="box" style="background-color: black;  flex: 3;">

        </div>
      </div>

      <div class="toolbar-buttons">
            <a-button class="toolbar-button_1">
              附件<PaperClipOutlined />
            </a-button>
            <a-button class="toolbar-button_2" style="padding: 8px;">生成知识图谱</a-button>
      </div>

</div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { ref, onMounted, provide } from "vue";
import { PaperClipOutlined } from '@ant-design/icons-vue';

const router = useRouter()

const handleBack = () => {
  router.back()
}



</script>


<style scoped>
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: url(@/assets/image/graph/graph_bg.png) no-repeat center top;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 90px 40px;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.toolbar-text {
  font-size: 20px;
  font-weight: 700;
  color: #333;
}

.toolbar-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.toolbar-button_1 {
  width: 100px;
  height: 32px;
  border-radius: 5px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(63, 140, 255, 1);
  padding: 8px 27px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 14px;
  color: rgba(63, 140, 255, 1);
  cursor: pointer;
}
.toolbar-button_1:hover {
  background:#3f8cff;
  color: #ffffff;
}

.toolbar-button_2 {
  width: 100px;
  height: 32px;
  border-radius: 5px;
  background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
  border: none;
  padding: 8px 27px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 14px;
  color: #ffffff;
  cursor: pointer;
}
.toolbar-button_2:hover {
  opacity: 0.8;
}

.box-container {
  width:100%;
  height: 750px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px 0;
  gap:20px;
}

.box {
  flex: 1;
  height: 100%;
  border-radius: 0.2vw;
  background-color: white;
}

</style>
