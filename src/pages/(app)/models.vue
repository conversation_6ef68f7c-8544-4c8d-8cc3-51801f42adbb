<script lang="ts">
  const fakeDataSource = Array.from({ length: 20 }, (_, i) => ({
    id: i.toString(),
    name: `模型 ${i + 1}`,
    source: ['Hugging Face', 'ModelScope', 'OpenAI'][i % 3],
    replicaNum: Math.floor(Math.random() * 10) + 1,
    activeReplicaNum: Math.floor(Math.random() * 10) + 1,
    createTime: new Date(Date.now() - i * 1000 * 60 * 60 * 24),
    framework: 'vllm',
    modelType: 'llm' as const,
  }))
</script>

<script setup lang="ts">
  import type { FormValues } from '@/components/models/ModelDeployForm.vue'
  import { modelsContextInjectionKey } from '@/utils/context/models'
  import { useFuse } from '@vueuse/integrations/useFuse'
  import { twMerge } from 'tailwind-merge'

  import ImgBackground from '@/assets/img/models-bg.jpg'
  import IconSearch from '~icons/ant-design/search-outlined'
  import IconTrash from '~icons/lucide/trash-2'

  definePage({
    meta: {
      roles: ['admin'],
    },
  })

  const isDeployModalOpen = ref(false)
  const deployFormInitalValues = shallowRef<Partial<FormValues>>({})
  const selectedModels = ref<string[]>([])
  const formAction = ref<'create' | 'update'>('create')

  provide(modelsContextInjectionKey, {
    isDeployModalOpen: readonly(isDeployModalOpen),
    setIsDeployModalOpen(value) {
      isDeployModalOpen.value = value
    },

    deployFormInitialValues: shallowReadonly(deployFormInitalValues),
    setDeployFormInitialValues(value) {
      deployFormInitalValues.value = value
    },

    selectedModels: shallowReadonly(selectedModels),
    setSelectedModels(value) {
      selectedModels.value = value
    },

    formAction: readonly(formAction),
    setFormAction(value) {
      formAction.value = value
    },
  })

  function deployNewModel() {
    deployFormInitalValues.value = {}
    formAction.value = 'create'
    isDeployModalOpen.value = true
  }

  const searchString = shallowRef('')
  const searchStringDebounced = refDebounced(searchString, 200)

  const { results } = useFuse(searchStringDebounced, fakeDataSource, {
    fuseOptions: { keys: ['name'], threshold: 0.3 },
  })
  const models = computed(() => {
    if (!searchStringDebounced.value) {
      return fakeDataSource
    }
    return results.value.map((result) => result.item)
  })
</script>

<template>
  <div
    class="h-full overflow-y-auto bg-cover bg-right bg-no-repeat [--page-px:40px]"
    :style="{ 'background-image': `url(${ImgBackground})` }"
  >
    <div class="px-(--page-px) pt-22.5 text-xl font-bold">模型管理</div>

    <div class="flex flex-wrap items-center gap-y-4 px-(--page-px) pt-7.5">
      <AInput
        class="max-w-[300px] rounded-full!"
        placeholder="搜索模型名称..."
        v-model:value="searchString"
      >
        <template #suffix>
          <IconSearch class="text-foreground-4" />
        </template>
      </AInput>

      <div class="text-foreground-3 ml-4">
        共有
        <span class="text-primary">{{ models.length }}</span>
        个筛选结果
      </div>

      <div class="ml-auto flex min-w-max items-center">
        <AButton
          type="primary"
          class="gradient-a-button w-[110px]"
          @click="deployNewModel"
        >
          部署模型
        </AButton>

        <AButton
          type="primary"
          ghost
          class="outline-a-button ml-2.5 flex! w-[110px] items-center gap-2"
          :disabled="selectedModels.length === 0"
        >
          <IconTrash />
          批量删除
        </AButton>
      </div>
    </div>

    <div class="px-(--page-px) py-5">
      <ModelTable :models="models" />
    </div>

    <AModal
      v-model:open="isDeployModalOpen"
      destroy-on-close
      title="部署模型"
      :ok-button-props="{
        htmlType: 'submit',
        // @ts-expect-error antdv poor typing
        form: 'model-deploy-form',
      }"
      :wrap-class-name="
        twMerge(
          '[&_.ant-modal-content]:px-[30px]! [&_.ant-modal-footer]:mt-[30px]!',
          '[&_.ant-modal-body]:-mr-[30px]! [&_.ant-modal-body]:pr-[30px]! [&_.ant-modal-body]:overflow-y-auto! [&_.ant-modal-body]:max-h-[650px]',
        )
      "
      :width="700"
    >
      <ModelDeployForm
        id="model-deploy-form"
        class="mt-[30px]!"
      />
    </AModal>
  </div>
</template>
