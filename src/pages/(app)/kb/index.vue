<script lang="ts">
  import type { KbCard } from '@/components/kb/KbCard.vue'

  const fakeDataSource: KbCard[] = Array.from({ length: 20 }, (_, i) => ({
    id: `${i + 1}`,
    name: `知识库 ${i + 1}`,
    description: '这是一段描述',
    fileNum: Math.floor(Math.random() * 10) + 1,
    courses: ['课程1', '课程2', '课程3'],
  }))
</script>

<script setup lang="ts">
  import { useFuse } from '@vueuse/integrations/useFuse'

  import IconSearch from '~icons/ant-design/search-outlined'

  const isCreateModalOpen = ref(false)

  const searchString = ref('')
  const searchStringDebounced = refDebounced(searchString, 200)

  const { results } = useFuse(searchStringDebounced, fakeDataSource, {
    fuseOptions: { keys: ['name'], threshold: 0.3 },
  })

  const kbs = computed(() => {
    if (!searchStringDebounced.value) {
      return fakeDataSource
    }
    return results.value.map((result) => result.item)
  })

  function createKb() {
    isCreateModalOpen.value = true
  }
</script>

<template>
  <div class="h-full overflow-y-auto [--page-px:40px]">
    <div class="text-foreground-2 px-(--page-px) pt-22.5 text-xl font-bold">知识库</div>
    <div class="flex flex-wrap items-center gap-y-4 px-(--page-px) py-7.5">
      <AInput
        class="max-w-[300px] rounded-full!"
        placeholder="搜索模型名称..."
        v-model:value="searchString"
      >
        <template #suffix>
          <IconSearch class="text-foreground-4" />
        </template>
      </AInput>

      <div class="text-foreground-3 ml-4">
        共有
        <span class="text-primary">{{ kbs.length }}</span>
        个筛选结果
      </div>

      <div class="ml-auto flex min-w-max items-center">
        <AButton
          class="gradient-a-button"
          @click="createKb"
        >
          创建知识库
        </AButton>
      </div>
    </div>

    <div class="@container px-(--page-px) pb-7.5">
      <div
        class="grid grid-cols-1 gap-5 @min-[820px]:grid-cols-2 @min-[1340px]:grid-cols-3 @min-[1860px]:grid-cols-4"
      >
        <KbCard
          v-for="kb in kbs"
          :kb="kb"
          :key="kb.id"
          class="flex-1"
        />
      </div>
    </div>

    <AModal
      v-model:visible="isCreateModalOpen"
      title="创建知识库"
      :ok-button-props="{
        htmlType: 'submit',
        // @ts-expect-error antdv poor typing
        form: 'kb-create-form',
      }"
      centered
    >
      <KbCreateForm id="kb-create-form" />
    </AModal>
  </div>
</template>
