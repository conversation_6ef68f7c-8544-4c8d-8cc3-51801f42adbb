<template>
  <div class="mian">
    <back :url="'/course/1'" />
    <div class="top">
      <div class="title">计算机网络-作业</div>
      <div>
        <a-button type="text" style="width: 82px;height: 32px;color: #fff;font-weight: 600;font-size: 14px;
              background: linear-gradient(135.84deg, #219FFF 0%, #0066FF 100%);" @click="handleCreate()">新建</a-button>
        <a-button type="text" style="width: 82px;height: 32px;border: #0066FF 1px solid;margin-left: 14px;
              color: #0066FF;font-size: 14px;">
          <template #icon>
            <DeleteOutlined />
          </template>
          删除
        </a-button>
      </div>
    </div>

    <div class="content" v-for="i in 3">
      <div class="content-header">
        <a-checkbox v-model:checked="checked" style="font-weight: 700;font-size: 16px;line-height: 16px;">作业名称</a-checkbox>
        <div class="action-group">
          <div class="action" style="margin-right: 20px;">
            <a-button type="text" class="action1">
              作业详情分析
            </a-button>
          </div>
          <div class="action">
            <a-button type="text" class="action1" @click="handleGrade()">阅卷</a-button>
          </div>
        </div>
      </div>
      <div class="content-body">
        <div class="item">
          <div class="item-title">班级</div>
          <div class="item-value">班级1、班级1、班级1</div>
        </div>
        <div class="item">
          <div class="item-title">有效时间</div>
          <div class="item-value">2024-06-01至2024-06-02</div>
        </div>
        <div class="item">
          <div class="item-title">已提交数量</div>
          <div class="item-value">1/2</div>
        </div>
        <div>
          <el-icon color="#3F8CFF" style="margin-right: 32px;" @click="open = true">
            <Edit />
          </el-icon>
          <el-icon color="#3F8CFF">
            <Delete />
          </el-icon>
        </div>
      </div>
    </div>


    <a-modal v-model:open="open" title="作业修改" :footer="null" width="700px">
      <div style="margin: 4vh 0 0 0;">
        <a-form :model="homework" name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" autocomplete="off">
          <a-form-item label="答题班级" name="class" v-bind="validateInfos.class">
            <a-select v-model:value="homework.class" mode="multiple" style="width: 100%" placeholder="请选择班级"
              :options="options" />
          </a-form-item>
          <a-form-item label="有效时间" v-bind="validateInfos.time">
            <a-range-picker v-model:value="homework.time" show-time style="width: 100%;" />
          </a-form-item>
          <a-form-item label="考试时长" v-bind="validateInfos.examTime">
            <div style="display: flex;align-items: center;">
              <a-input-number id="inputNumber" v-model:value="homework.examTime" :min="1" :max="99999"
                placeholder="考试时长" />
              <div style="margin-left: 4px;">分钟</div>
            </div>
          </a-form-item>
          <a-form-item label="乱序设置" v-bind="validateInfos.disorder">
            <a-radio-group v-model:value="homework.disorder" name="radioGroup">
              <a-radio value="1">试题乱序</a-radio>
              <a-radio value="2">选项乱序</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="关联章节" v-bind="validateInfos.chapter">
            <a-tree-select v-model:value="homework.chapter" show-search style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="请关联章节" allow-clear
              tree-default-expand-all :tree-data="treeData" :field-names="{
                children: 'children',
                value: 'value',
                label: 'title',
              }" tree-node-filter-prop="title">
            </a-tree-select>
          </a-form-item>
          <a-form-item label="作业描述" v-bind="validateInfos.description">
            <a-textarea v-model:value="homework.description" placeholder="请输入作业描述" :rows="4" />
          </a-form-item>
          <div style="display: flex;justify-content: flex-end">
              <a-form-item>
                <a-space>
                    <a-button @click=" open = false">取消</a-button>
                    <a-button type="primary" html-type="submit" @click="onSubmit" style=""
                    :loading="loadingSubmit">确定</a-button>
                </a-space>
              </a-form-item>
          </div>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { DeleteOutlined } from '@ant-design/icons-vue';
import type { TreeSelectProps } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';
type RangeValue = [Dayjs, Dayjs];
import { Form } from 'ant-design-vue';
import { message } from 'ant-design-vue';
const useForm = Form.useForm
import back from '@/components/ui/back.vue';
import { ref, reactive } from 'vue';
// import router from '@/router';
import { useRouter } from 'vue-router'
const router = useRouter()
const checked = ref(false);

const open = ref(false); //编辑弹窗
const homework = reactive({
  class: [],//班级
  time: [null, null] as unknown as RangeValue,//有效时间
  examTime: '',//答题时间
  disorder: '',//乱序
  chapter: '',//章节
  description: ''//描述
});
const options = reactive([
  { value: 1, label: '班级一' },
  { value: 2, label: '班级二' },
  { value: 3, label: '班级一' },
  { value: 4, label: '班级二' },
])
const treeData = ref<TreeSelectProps['treeData']>([
  {
    title: '课程章 1',
    value: 'charpter1',
    children: [
      {
        title: '课程节 1-0',
        value: 'charpter1-0',
        children: [
          {
            title: '制造',
            value: 'leaf1',
          },
          {
            title: '智能制造',
            value: 'leaf2',
          },
        ],
      },
      {
        title: '课程节 1-1',
        value: 'charpter1-1',
      },
    ],
  },
]);
const homeworkRules = reactive({
  class: [{ required: true, message: '请选择答题班级', trigger: 'blur' }],
  time: [{ required: true, message: '请选择有效时间', trigger: 'change' }],
  examTime: [{ required: true, message: '请输入考试时长', trigger: 'change' }],
  disorder: [{ required: true, message: '请选择乱序设置', trigger: 'change' }],
  chapter: [{ required: true, message: '请关联章节', trigger: 'change' }],
  description: [{ required: true, message: '请输入作业描述', trigger: 'change' }],
})
const { resetFields, validate, validateInfos } = useForm(homework, homeworkRules);//验证课程表单提交
const loadingSubmit = ref(false)
const onSubmit = () => {
  validate().then(() => {
    onSubmitSuccess()
  }).catch(err => {
    console.log('error', err);
  });
};
const onSubmitSuccess = () => {
  console.log('提交成功', homework);
  loadingSubmit.value = true
  // const formData = new FormData();
  // formData.append('title', course.title);
  // formData.append('semester', JSON.stringify(course.semester)); // 转为字符串
  // formData.append('konwelage', course.konwelage);
  // formData.append('image', course.image);
  // formData.append('teacher', JSON.stringify(course.teacher));
  // formData.append('role', course.role);

  // courseAdd(formData).then(res => {
  //     loadingSubmit.value = false
  //     open.value = false
  //     course.image  = ''
  //     message.success('创建成功')
  //     resetFields();
  // }).catch(err => {
  //     loadingSubmit.value = false
  //     message.error('创建失败')
  // })
};

//阅卷
function handleGrade(){
  router.push({ path: '/homework/grade' })
}
//新建作业
function handleCreate(){
  router.push({ path: '/homework/create' })
}

</script>
<style lang="scss" scoped>
.mian {
  min-height: 100vh;
  background: #F0F9FF;
  display: flex;
  flex-direction: column;
  padding: 0 40px;
  background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
  background-repeat: no-repeat;
  background-size: 100% 50%;
  background-position: center bottom;
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title {
    font-size: 24px;
    font-weight: 700;
    line-height: 12px;
    margin: 20px 0;
  }
}

.content {
  border-radius: 2.06px;
  background: #FFFFFF;
  // border: 2px solid #FFFFFF;
  box-shadow: 0px 2px 19px  rgba(29, 79, 153, 0.05);
  margin-bottom:  20px;
  padding:20px;
  height: 161px;
  max-width: 1759px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .action-group {
      display: flex;
    }

    .action {
      border-radius: 3px;
      background: rgba(63, 140, 255, 1);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 5px 10px;
      height: 24px;
      .action1{
        font-size: 14px;
        line-height: 14px;
        color: #fff;
        font-weight: 500;
        padding: 0;
      }
    }
  }

  .content-body {
    display: flex;
    // height: 82px;
    align-items: center;
    justify-content: space-between;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(227, 240, 252, 1);
    padding: 20px 24px 20px 40px;
    margin-top: 20px;

    .item {
      text-align: center;

      .item-title {
        color: #666666;
        font-size: 14px;
        font-weight: 400;
        line-height: 10px;
      }

      .item-value {
        margin-top: 10px;
        font-weight: 500;
        font-size: 14px;
        letter-spacing: 0px;
        line-height: 14px;
      }
    }
  }
}
</style>
