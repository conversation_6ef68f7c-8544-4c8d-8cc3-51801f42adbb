<template>
  <div class="mian">
    <back />
    <div class="top">
      <div class="title text-2xl">计算机网络-作业-作业1</div>
      <div class="flex" style="justify-content: space-between;margin-top: 1.6vh;">
        <div>
            <a-space>
                <a-select
                    ref="select"
                    v-model:value="valueCalss"
                    style="width: 5.7vw;"
                    :options="classOption"
                    placeholder="请选择班级"
                    allowClear
                ></a-select>
                <a-select
                    ref="select"
                    v-model:value="workStatus"
                    style="width: 5.7vw;"
                    :options="workStatusOption"
                    placeholder="选择作业状态"
                    allowClear
                ></a-select>
                <el-input v-model="searchValue" style="width: 15vw;min-height: 32px"
                    placeholder="请输入" :suffix-icon="Search" />
                <div class="text-sm">共有<text style="color: rgba(63, 140, 255, 1);">6</text>条结果</div>
            </a-space>
        </div>
        <div class="flex1">
            <div class="text-sm">
                已提交/全部：
                <span>10/20</span>
            </div>
            <a-button type="text" class="text-sm" style="width: 82px;color: #fff;font-weight: 600;padding: 1px 4px;
                background: linear-gradient(135.84deg, #219FFF 0%, #0066FF 100%);">发布</a-button>
        </div>
      </div>
    </div>

    <div class="table">
        <div class="table-head height text-sm" style="font-weight:600">
            <a-checkbox
                v-model:checked="state.checkAll"
                :indeterminate="state.indeterminate"
                @change="onCheckAllChange"
                style="width: 10vw;"
            />
            <div>姓名</div>
            <div class="flex">
                学号
                <el-icon  color="#3F8CFF"><DCaret /></el-icon>
            </div>
            <div>提交时间</div>
            <div>状态</div>
            <div class="flex">
                成绩
                <el-icon  color="#3F8CFF"><DCaret /></el-icon>
            </div>
            <div>操作</div>
        </div>
        <div class="table-head height text-sm">
            <a-checkbox-group v-model:value="state.checkedList" :options="plainOptions" />
            <div>姓名</div>
            <div class="flex">
                学号
                <el-icon  color="#3F8CFF"><DCaret /></el-icon>
            </div>
            <div>提交时间</div>
            <div>状态</div>
            <div class="flex">
                成绩
                <el-icon  color="#3F8CFF"><DCaret /></el-icon>
            </div>
            <div>操作</div>
        </div>


    </div>
  </div>
</template>

<script lang="ts" setup>
import type { SelectProps } from 'ant-design-vue';
import { Search } from '@element-plus/icons-vue'
import back from '@/components/ui/back.vue';
import { ref, reactive, watch } from 'vue';



const valueCalss = ref(null);  //班级选择
const classOption = ref<SelectProps['options']>([
  {
    value: '1',
    label: '班级1',
  },{
    value: '2',
    label: '班级2',
  },{
    value: '3',
    label: '班级3',
  }
]);
const workStatus = ref(null);  //作业状态
const workStatusOption = ref<SelectProps['options']>([
  {
    value: '1',
    label: '未提交',
  },{
    value: '2',
    label: '阅卷中',
  },{
    value: '3',
    label: '已阅卷',
  },{
    value: '4',
    label: '已发布',
  }
]);
const searchValue = ref(null);

const plainOptions = [''];
const state = reactive({
  indeterminate: false,
  checkAll: false,
  checkedList: [],
});
const onCheckAllChange = (e: any) => {
  Object.assign(state, {
    checkedList: e.target.checked ? plainOptions : [],
    indeterminate: false,
  });
};
watch(
  () => state.checkedList,
  val => {
    state.indeterminate = !!val.length && val.length < plainOptions.length;
    state.checkAll = val.length === plainOptions.length;
  },
);


</script>
<style lang="scss" scoped>
:deep(.ant-select-selector){
    border-radius: 20px;
}
:deep(.el-input__wrapper) {
    border-radius: 50px !important;
}
.mian {
  min-height: 100vh;
  background: #F0F9FF;
  display: flex;
  flex-direction: column;
  padding: 0 2.3vw;
  background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
  background-repeat: no-repeat;
  background-size: 100% 50%;
  background-position: center bottom;
}

.top {
    .flex1{
        display: flex;
        align-items: flex-end;
    }

  .title {
    font-weight: 700;
  }
  .flex{
    display: flex;
    align-items: center;
    span{
        font-weight: 600;
        margin-right: 20px;
    }
  }
}

.table {
  margin-top: 1.1vh;
  background: #fff;
  border-radius: 20px;
  padding: 0 1.1vw;
  .table-head{
    display: flex;
    align-items: center;
    .flex{
        display: flex;
        align-items: center;
    }
    // div::nth-child(1){
    //     width: 20%;
    // }
  }
  .height{
    height: 5.5vh;
    border-bottom: 1px solid #E5E5E5;
  }
}

</style>
