<template>
  <div class="mian">
    <back :url="'/course/1'" />
    <div class="top">
      <div class="title">计算机网络-作业-新建作业</div>
    </div>
    
    <div>
      <div>
        <div>题库选题作业</div>
        <div>将题库试题发布给学生学习</div>
      </div>
    </div>

    


   
  </div>
</template>

<script lang="ts" setup>
import { DeleteOutlined } from '@ant-design/icons-vue';
import type { TreeSelectProps } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';
type RangeValue = [Dayjs, Dayjs];
import { Form } from 'ant-design-vue';
import { message } from 'ant-design-vue';
const useForm = Form.useForm
import back from '@/components/ui/back.vue';
import { ref, reactive } from 'vue';
// import router from '@/router';
import { useRouter } from 'vue-router'
const router = useRouter()
const checked = ref(false);

const open = ref(false); //编辑弹窗
const homework = reactive({
  class: [],//班级
  time: [null, null] as unknown as RangeValue,//有效时间
  examTime: '',//答题时间
  disorder: '',//乱序
  chapter: '',//章节
  description: ''//描述
});
const options = reactive([
  { value: 1, label: '班级一' },
  { value: 2, label: '班级二' },
  { value: 3, label: '班级一' },
  { value: 4, label: '班级二' },
])
const treeData = ref<TreeSelectProps['treeData']>([
  {
    title: '课程章 1',
    value: 'charpter1',
    children: [
      {
        title: '课程节 1-0',
        value: 'charpter1-0',
        children: [
          {
            title: '制造',
            value: 'leaf1',
          },
          {
            title: '智能制造',
            value: 'leaf2',
          },
        ],
      },
      {
        title: '课程节 1-1',
        value: 'charpter1-1',
      },
    ],
  },
]);
const homeworkRules = reactive({
  class: [{ required: true, message: '请选择答题班级', trigger: 'blur' }],
  time: [{ required: true, message: '请选择有效时间', trigger: 'change' }],
  examTime: [{ required: true, message: '请输入考试时长', trigger: 'change' }],
  disorder: [{ required: true, message: '请选择乱序设置', trigger: 'change' }],
  chapter: [{ required: true, message: '请关联章节', trigger: 'change' }],
  description: [{ required: true, message: '请输入作业描述', trigger: 'change' }],
})
const { resetFields, validate, validateInfos } = useForm(homework, homeworkRules);//验证课程表单提交
const loadingSubmit = ref(false)
const onSubmit = () => {
  validate().then(() => {
    onSubmitSuccess()
  }).catch(err => {
    console.log('error', err);
  });
};
const onSubmitSuccess = () => {
  console.log('提交成功', homework);
  loadingSubmit.value = true
  // const formData = new FormData();
  // formData.append('title', course.title);
  // formData.append('semester', JSON.stringify(course.semester)); // 转为字符串
  // formData.append('konwelage', course.konwelage);
  // formData.append('image', course.image);
  // formData.append('teacher', JSON.stringify(course.teacher));
  // formData.append('role', course.role);

  // courseAdd(formData).then(res => {
  //     loadingSubmit.value = false
  //     open.value = false
  //     course.image  = ''
  //     message.success('创建成功')
  //     resetFields();
  // }).catch(err => { 
  //     loadingSubmit.value = false
  //     message.error('创建失败')
  // })
};

//阅卷
function handleGrade(){
  router.push({ path: '/homework/grade' })
}

</script>
<style lang="scss" scoped>
.mian {
  min-height: 100vh;
  background: #F0F9FF;
  display: flex;
  flex-direction: column;
  padding: 0 40px;
  background-image: url('@/assets/image/img/homeworkitem/homeworkbg.png');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center bottom;
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title {
    font-size: 24px;
    font-weight: 700;
    line-height: 12px;
    margin: 20px 0;
  }
}

</style>