<template>
  <div class="page">
    <el-main class="page-content">
      <div @click="handleBack" class="back-btn">
        <img style="width: 12px;height: 11px;margin-right: 8px;" src="@/assets/image/ArrowLeft.svg" />
        <span style="font-size: 14px;line-height: 14px;">返回</span>
      </div>
      <h1 class="text">PPT管理</h1>
      <div class="toolbar">
        <div class="toolbar-left">
          <div style="display: flex;">
            <a-button type="text" class="addbut" @click="handleAddPPT">新建</a-button>
            <a-upload :file-list="fileList" :before-upload="beforeUpload" accept=".ppt,.pptx"  :showUploadList="false">
              <a-button type="primary" ghost :loading="uploadloading" style="width: 82px;margin: 0 10px;">上传</a-button>
            </a-upload>

            <a-button type="primary" ghost style="width: 82px;" @click="pptRecycle">回收站</a-button>
            <a-button type="primary" style="margin-left: 10px;" @click="delMessage(selectedIds)" ghost
              :icon="h(DeleteOutlined)">删除</a-button>
          </div>
          <!-- <div style="display: flex;">
            <a-button type="primary" ghost :icon="h(DownloadOutlined)" @click="uploadPPt">下载</a-button>
            <a-button type="primary" style="margin-left: 10px;" @click="delMessage(selectedIds)" ghost
              :icon="h(DeleteOutlined)">删除</a-button>
          </div> -->
        </div>
        <div class="toolbar-right">
          <el-select v-model="valueSelect" placeholder="课程章节" style="width: 100px;min-height: 32px;"
            suffixIcon="CaretBottom">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-input v-model="searchValue" style="width: 302px;min-height: 32px;" placeholder="请输入"
            @keyup.enter="getPptList" >
            <template #suffix>
              <el-icon @click="getPptList"><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
      <a-spin :spinning="spinning">
        <div class="table">
          <div class="table-head height" style="font-weight:600">
            <div class="item" style="max-width: 56px;">
              <a-checkbox v-model:checked="state.checkAll" :indeterminate="state.indeterminate"
                @change="onCheckAllChange" />
            </div>
            <div class="item" style="width: 400px;">文件名</div>
            <div class="item" style="width: 130px;">创建者</div>
            <div class="item" style="width: 140px;">更新时间</div>
            <div class="item" style="max-width: 118px;">操作</div>
          </div>

          <div class="center" v-if="files?.length === 0">
            <img src="@/assets/image/zanwu/noData.png" style="width: 385px;height: 379px;" />
          </div>
          <div v-else>
            <div class="table-head height1" v-for="item in files" :key="item.id">
              <div class="item" style="max-width: 58px;">
                <a-checkbox :checked="selectedIds.includes(item.id)"
                  @change="(e: any) => handleCheckboxChange(item.id, e.target.checked)" />
              </div>
              <div class="item file-name titlew">
                <div style="" class="file-name-text">
                  {{ item.title }}
                </div>
                <span class="edit-icon" style="">
                  <a-tooltip placement="bottom">
                    <template #title>
                      <span>编辑</span>
                    </template>
                    <img style="cursor: pointer;width: 14px;height: 14px;" src="@/assets/image/img/edittable.png" @click="editPPT(item)" />
                  </a-tooltip>
                </span>
              </div>
              <div class="item" style="width: 130px;">
                {{ item.author || '无' }}
              </div>
              <div class="item" style="width: 140px;">
                {{ formatDate(item.updated_at) }}
              </div>
              <div class="item" style="max-width: 118px;display: flex;align-items: center;">
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>下载</span>
                  </template>
                  <DownloadOutlined style="color: #3F8CFF;font-size: 14px;" @click="uploadPPt(item)" />
                </a-tooltip>
                <a-tooltip placement="bottom">
                  <template #title>
                    <span>删除</span>
                  </template>
                  <img style="cursor: pointer;margin-left: 16px;" src="@/assets/image/delete.svg" @click="delMessage(item.id)" />
                </a-tooltip>
              </div>
            </div>
          </div>

          <div class="pagination">
            <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
              layout="->, prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </div>
      </a-spin>
    </el-main>

  </div>
</template>

<script lang="ts" setup>
import { formatDate } from '@/utils/util'
import { DeleteOutlined, DownloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import axios from "axios";
//引入接口
import { pptList, ppt_softDelete, ppt_hardDelete, pptUpload } from '@/services/api/ppt'
import { usePagination } from '@/hooks/usePagination'; //解构分页参数
import { ref, computed, markRaw, onMounted, reactive, watch, h , createVNode} from 'vue'
import { Search, WarningFilled, } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { message, Modal} from 'ant-design-vue';
import { showDeleteConfirm } from '@/hooks/useDeleteConfirm'
import type { UploadProps } from 'ant-design-vue';

const router = useRouter()

const fileList = ref<UploadProps['fileList']>([]);
const uploadloading = ref(false)
const beforeUpload: UploadProps['beforeUpload'] = file => {
  uploadloading.value = true
  fileList.value = [...(fileList.value || []), file];
  console.log(file,'file')
  const params = new FormData();
  params.append('file', file);
  params.append('title', fileList.value[0].name);
  pptUpload(params).then(res => {
    message.success('上传成功');
    uploadloading.value = false
    getPptList()
  }).catch(err => {
    message.error('上传失败');
    uploadloading.value = false
  })
  return false;
};


const handleAddPPT = () => {
  window.open(`${import.meta.env.VITE_PPT_FRONTEND_URL}?type=pptSquare/pptManage`, '_parent');
}

//当前页所有id
const currentPageIds = computed(() => files.value?.map(item => item.id) || []);
// 已选中的id
const selectedIds = ref<number[]>([]);
// 处理单个复选框的选择变化
const handleCheckboxChange = (id: number, checked: boolean) => {
  if (checked) {
    selectedIds.value.push(id);
  } else {
    selectedIds.value = selectedIds.value.filter((item) => item !== id);
  }
};
// 全选操作
const onCheckAllChange = (e: any) => {
  const isChecked = e.target.checked;
  if (isChecked) {
    selectedIds.value = [...new Set([...selectedIds.value, ...currentPageIds.value])]; // 合并去重
  } else {
    selectedIds.value = selectedIds.value.filter(id => !currentPageIds.value.includes(id));
  }
  state.indeterminate = false;
};
const state = reactive({
  indeterminate: false,
  checkAll: false,
  checkedList: [],
});

//定义列表数据结构
interface fileList {
  id: number;
  title: string;
  author: string;
  updated_at: string;
}
// 模拟数据
const files = ref<fileList[]>()
const total = ref(0)
const { currentPage, pageSize, handleSizeChange, handleCurrentChange } = usePagination(1, 10, total.value, getPptList);
const spinning = ref(false)

const valueSelect = ref(null)
const options = reactive([
  { value: 1, label: '章节一' },
  { value: 2, label: '章节二' },
  { value: 3, label: '章节一' },
  { value: 4, label: '章节二' },
])
const searchValue = ref(null)

onMounted(() => {
  getPptList() //  获取PPT列表
})

// 获取PPT列表
async function getPptList() {
  const params = {
    page: currentPage.value,
    page_size: pageSize.value,
    is_deleted: 'false',
    title: searchValue.value
  }
  try {
    spinning.value = true
    const res = await pptList(params)
    files.value = res.data.results
    total.value = res.data.count
    spinning.value = false
  } catch (error) {
    console.error('获取PPT列表失败:', error)
  }
}


const handleBack = () => {
  //需要动态配置课程id
  router.push('/course/1')
}

//删除
const delMessage = async (params: any) => {
  const ids = Array.isArray(params) ? params : [params]
  if(ids.length == 0){
    message.error('请选择要删除的记录');
    return
  }
  const confirmed = await showDeleteConfirm('确定要删除这条记录吗？');
  if (confirmed) {
    softDelete(ids)
  }
  console.log(confirmed);
}
async function softDelete(id: number[]) {
  const params = {
    "ids": id,  //传单个及单个删除
    "action": "delete"  //restore:恢复，delete：软删除
  }
  try {
    const res = await ppt_softDelete(params)
    getPptList()
  } catch (error) {
    message.error('删除失败')
  }
}

function pptRecycle() {
  router.push('/pptSquare/pptRecycle')
};

async function uploadPPt(item: any) {
  console.log(item);
  if(item.file == null){
    const url = `${import.meta.env.VITE_PPT_FRONTEND_URL}?type=pptSquare/pptManage&id=${item.id}`
    window.open(url, '_parent');
  }else{
    window.open(item.file, '_blank');
    // try {
    //   const response = await axios.get(item.file, {
    //     responseType: 'blob', // 关键：指定响应类型为二进制流
    //   });
    //   const blob = new Blob([response.data]);
    //   const link = document.createElement('a');
    //   link.href = URL.createObjectURL(blob);
    //   link.download = item.title; // 设置下载的文件名
    //   link.click();
    //   URL.revokeObjectURL(link.href); // 释放内存
    // } catch (error) {
    //   console.error('下载失败', error);
    // }
  }
}

const editPPT = (item: any) => {
  console.log(item.file);
  if(item.file != null){
    message.error('自上次的PPT不支持编辑!')
    return
  }
  const url = `${import.meta.env.VITE_PPT_FRONTEND_URL}?type=pptSquare/pptManage&id=${item.id}`
  window.open(url, '_parent');
}

</script>

<style scoped lang="scss">
:deep(.el-input__suffix-inner) {
    cursor: pointer;
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .page-content {
    width: 100%;
    height: 100vh;
    overflow: auto;
    background: url(@/assets/image/bg1.png) no-repeat center top;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 0 40px;

    .back-btn {
      cursor: pointer;
      width: 60px;
      margin-top: 50px;
      color: #333;
      display: flex;
      align-items: center;
    }
  }
}





.text {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 12px;
  margin: 20px 0 20px;
}

.toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
      max-width: 1720px;
    min-width: 900px;

  .toolbar-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 36px;

    .addbut {
      width: 82px;
      color: #fff;
      background: linear-gradient(135deg, #219FFF 0%, #0066FF 100%);
    }
  }

  .toolbar-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }
}




:deep(.el-select__wrapper) {
  border-radius: 95px;
}


:deep(.el-input__wrapper) {
  border-radius: 94px;
}


.table {
  background: #fff;
  border-radius: 4.79px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 19px rgba(29, 79, 153, 0.05);
  padding: 0 20px 20px;
  min-width: 900px;
  max-width: 1720px;

  .table-head {
    // display: flex;
    align-items: center;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    /* 禁止换行 */
    overflow-x: auto;
    /* 允许水平滚动（如果需要） */
    font-size: 14px;
    padding-left: 12px;

    .item {
      flex: 1 0 auto;
      white-space: nowrap;
      /* 防止文字换行 */
    }

    .flex {
      display: flex;
      align-items: center;
    }

    .titlew {
      width: 400px;
      position: relative;
      height: 56px;
      line-height: 56px;
    }

    .file-name-text {
      max-width: 330px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden
    }

    .file-name .edit-icon {
      opacity: 0;
      transition: opacity 0.3s ease;
      position: absolute;
      left: 345px;
      top: 50%;
      transform: translateY(-50%);
    }

    .file-name:hover .edit-icon {
      opacity: 1;
    }
  }

  .height {
    height: 56px;
    border-bottom: 1px solid #E5E5E5;
  }

  .height1 {
    height: 58px;
    border-bottom: 1px solid #E5E5E5;
    color: #666666;

    &:hover {
      background: rgba(63, 140, 255, 0.05);
    }
  }
}

.pagination {
  margin: 20px 0 12px 0;
  text-align: center;
}

:deep(.el-pager li) {
  min-width: 22px !important;
  height: 22px !important;
}

:deep(.el-pager li.is-active) {
  font-size: 14px;
  color: #ffffff;
  background-color: #3f8cff;
}
</style>
