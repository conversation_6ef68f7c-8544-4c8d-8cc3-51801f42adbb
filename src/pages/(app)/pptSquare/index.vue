<template>
  <div class="page">
    <div class="page-header">
      <div @click="handleBack" class="back-btn">
        <img style="width: 14px;height: 10px;" src="@/assets/image/ArrowLeft.svg"/>
        <span class="back-text">返回</span>
      </div>
    </div>
    <div class="page-content">
        <h1 class="name">PPT广场</h1>
        <p class="desc">
          介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资介绍性文字薪资
        </p>
        <button  class="primary-btn" @click="creatPPT">
            <img style="width: 1.2vw;" src="@/assets/image/pptSquare/svg.svg"/>
            <span>生成PPT</span>
        </button>
    </div>
    <div class="page-cards">
        <div class="ppt-card">
            <div class="image-wrapper">
                <img src="@/assets/image/pptSquare/png1.png"/>
            </div>
            <div class="text-wrapper">
                <h2 class="title">PPT管理</h2>
                <p class="subtitle">PowerPoint Management</p>
                <button class="enter-btn" @click="interPPT">立即进入</button>
            </div>
        </div>
        <div class="ppt-card">
            <div class="image-wrapper">
                <img src="@/assets/image/pptSquare/png2.png"/>
            </div>
            <div class="text-wrapper">
                <h2 class="title">模板管理</h2>
                <p class="subtitle">Teaching Plan Management</p>
                <button class="enter-btn" @click="interModule">立即进入</button>
            </div>
        </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'

const router = useRouter()

function creatPPT() {
  window.open(`${import.meta.env.VITE_PPT_FRONTEND_URL}?type=pptSquare/pptManage`, '_blank');
};
function interPPT() {
  router.push('/pptSquare/pptManage')
};
function interModule() {

};
function handleBack() {
  router.push('/aiSpace')
}

</script>

<style scoped lang="scss">

.page {
  width: 100%;
  height: 100vh;
  background: url(@/assets/image/pptSquare/bg.png) no-repeat center top;
  background-size: cover;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;

  .page-header{
    width: 100%;
    padding: 0 0 0 13vw;

    .back-btn {
      width: 60px;
      cursor: pointer;
      padding-top: 30px;
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }

  .page-content {
    width: 100%;
    padding: 15vh 13vw 6.48vh 13vw;

    .name {
      font-size: 2vw;
      margin: 0 0 30px 0;
      font-weight: 700;
    }

    .desc {
      width: 39.6vw;
      height:11.1vh;
      font-size: 1vw;
      font-weight: 500;
      line-height: 3.7vh;
      margin: 0 0 5.4vh 0;
      color: #333;
    }

    .primary-btn {
      width: 8vw;
      height: 4.8vh;
      display: flex;
      align-items: center;
      gap: 0.6vw;
      cursor: pointer;
      border: none;
      background:  linear-gradient(135.84deg, rgba(140, 246, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
      color: white;
      padding: 0.9vw;
      border-radius: 30px;
      font-size: 1vw;
      font-weight: 500;
      letter-spacing: 0px;
      line-height: 1vh;
    }

    .primary-btn:hover {
      opacity: 0.8;
    }
  }

  .page-cards {
    width: 100%;
    display: flex;
    flex-direction: row;
    /* flex-wrap: wrap; */
    gap: 2.7vw;
    justify-content: space-between;
    padding: 0 13vw;

    .ppt-card {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      background-color: #fff;
      border-radius: 10px;
      box-sizing: border-box;
      width: 48%;
      height: 27vh;
      padding: 4vh 4vh;
      justify-content: space-around;
      box-shadow: 0px 2px 13px  rgba(7, 76, 179, 0.1);
    }

    .image-wrapper img {
      width: 13vw;
    }

    .text-wrapper {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;
    }

    .title {
      font-size: 1.25vw;
      font-weight: bold;
      background-image: linear-gradient(90deg, rgb(0, 102, 255) 0%, rgba(27, 148, 255) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 10px;
    }

    .subtitle {
      min-width: 250px;
      font-size: 1vw;
      font-weight: bold;
      background-image:  linear-gradient(90deg, rgba(194, 194, 194, 0.3) 0%, rgba(27, 148, 255, 0.3) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 2.7vh;
    }

    .enter-btn {
      padding: 0.7vh 1vw;
      font-size: 0.8vw;
      color: #1677ff;
      border: 0.1vw solid #1677ff;
      border-radius: 94px;
      background: transparent;
      cursor: pointer;
      transition: all 0.3s;
    }

    .enter-btn:hover {
      background: #1677ff;
      color: white;
    }
  }

}
</style>
