<template>
  <a-spin :spinning="spinning">
    <div class="page">
      <!-- 顶部返回 -->
      <div class="page-header">
        <div class="left">
          <div
            @click="handleBack"
            class="back-btn"
          >
            <img
              style="width: 14px; height: 10px"
              src="@/assets/image/ArrowLeft.svg"
            />
            <span class="back-text">返回</span>
          </div>
        </div>
        <div class="center">教案生成描述</div>
        <div class="right"></div>
      </div>

      <div class="page-content">
        <!-- 主要内容区域 -->
        <div class="main-content">
          <div class="chat-window">
            <div class="avatar">
              <img
                src="@/assets/image/avatar.svg"
                alt="机器人图标"
                class="robot-icon"
              />
            </div>

            <img
              v-if="loading"
              src="@/assets/image/3dots.svg"
              class="robot-icon"
            />

            <div
              class="bubble"
              v-else
            >
              <div class="subtitle">根据教案模板生成教案</div>

              <div
                class="chat-content"
                v-html="parseMarkdown(content)"
              ></div>
            </div>
          </div>

          <!-- 保存按钮 -->
          <button
            class="save-btn"
            @click="createTeachPlan()"
          >
            生成教案
          </button>

          <!-- 底部输入区域 -->
          <div class="input-wrapper">
            <input
              type="text"
              placeholder="帮我根据该模版生成出相应内容"
              v-model="keyword"
              class="input-field"
              @enter="createTeachPlan()"
            />

            <div class="input-actions">
              <span
                class="send"
                @click="createTeachPlan()"
              >
                <img
                  style="width: 1.2vw; height: 1.2vw"
                  src="@/assets/image/plane.svg"
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<script lang="ts" setup>
  import { parseMarkdown } from '@/utils/markdownParser'
  import { ref, reactive } from 'vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  const handleBack = () => {
    router.back()
  }

  const spinning = ref(false)
  const keyword = ref('帮我根据该模版生成出相应内容')
  const loading = ref(false)

  const content = window.history.state.content as string

  //生成教案
  const createTeachPlan = () => {
    router.push({
      //  path: '/teachPlanEditor', query: { content: templateItems.value[selectedIndex.value] }
      path: '/LessonPlan/teachPlanEditor',
      state: {
        desc: keyword.value,
        template: content,
      },
    })
  }
</script>

<style scoped>
  .page {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 6vh;
    background-color: #ffffff;
    box-sizing: border-box;
  }

  .left,
  .right {
    height: 6vh;
    display: flex;
    align-items: center;
  }

  .center {
    flex: 1;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
  }

  .back-btn {
    cursor: pointer;
    margin: 0 2vw;
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .page-content {
    background: url(@/assets/image/bg1.png) no-repeat center top;
    background-size: cover;
    box-sizing: border-box;
    /* padding: 0 2vw; */
    flex: 1;
    /* 关键属性 - 填充剩余空间 */
  }

  .main-content {
    background-color: transparent;
    border-radius: 8px;
    padding: 1vw 0;
    width: 45%;
    margin: 0 auto;
  }

  .chat-window {
    display: flex;
    flex-direction: row;
    gap: 1vw;
  }

  .avatar {
    flex-shrink: 0;
    width: 2vw;
    height: 2vw;
    text-align: center;
    line-height: 2vw;
    font-size: 1rem;
    user-select: none;
  }

  .bubble {
    flex: 1;
    height: 60vh;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 1vw;
    border-radius: 0.8rem;
    background-color: #ffffff;
    color: #000;
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.4;
    font-size: 1rem;
  }

  .subtitle {
    padding-bottom: 1vw;
    border-bottom: 0.1vw solid rgba(229, 229, 229, 1);
    margin-bottom: 1vw;
  }

  .chat-content {
    height: 85%;
    overflow: auto;
    scrollbar-width: none;
  }

  .icon-header {
    text-align: center;
    margin-bottom: 30px;
  }

  .robot-icon {
    width: 2vw;
    margin-bottom: 10px;
  }

  .text {
    width: 80px;
    color: #333;
  }

  input {
    flex: 1;
    border: none;
    border-bottom: 1px solid #eee;
    padding: 5px;
    outline: none;
  }

  .save-btn {
    background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
    color: white;
    border: none;
    padding: 8px 30px;
    border-radius: 4px;
    cursor: pointer;
    margin: 1vw 0 1vw 3vw;
  }

  .input-wrapper {
    display: flex;
    background-color: #ffffff;
    padding: 0.8vw;
    border-radius: 0.5vw;
    box-shadow: 1vw;
  }

  .input-field {
    flex: 1;
    border: none;
    outline: none;
    padding: 0 1vw;
    font-size: 1vw;
    border-radius: 0.5vw;
    background-color: #ffffff;
  }

  .input-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .el-dropdown-link {
    width: 6vw;
    height: 4vh;
    font-size: 0.8vw;
    font-weight: 500;
    gap: 0.2vw;
    cursor: pointer;
    background-color: white;
    user-select: none;
    border: 0.1vw solid #dcdfe6;
    padding: 0.5vh 0.8vw;
    border-radius: 94px;
    display: inline-flex;
    align-items: center;
  }

  .attach,
  .send {
    cursor: pointer;
  }

  /* .attach {
  display: inline-flex;
  align-items: center;
} */

  .send {
    width: 3.5vw;
    height: 4vh;
    background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
    color: white;
    padding: 0.5vh 0.8vw;
    border-radius: 2vw;
    margin-left: 1vw;
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }
</style>
