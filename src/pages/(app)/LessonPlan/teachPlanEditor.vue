<template>
  <a-spin :spinning="spinning">
      <div class="page">

        <!-- 顶部返回 -->
        <div class="page-header">
          <div class="left">
            <div @click="handleBack" class="back-btn">
              <img style="width: 14px;height: 10px;" src="@/assets/image/ArrowLeft.svg"/>
              <span class="back-text">返回</span>
            </div>

                <span v-if="!editing" @click="startEditing" class="topTitle">{{ title }}</span>
                <input
                  v-else
                  v-model="teachPlanTitle"
                  @blur="saveTitle"
                  ref="inputRef"
                  class="topTitle"
                />

            <div class="date">已保存于 {{ currentTime }}</div>
          </div>
          <div class="right">
            <button class="btn1" @click="savePlan()">保存加关联</button>
          </div>
        </div>

        <div class="page-content">

          <AiEditors :teachPlanData="teachPlanData" :manageData="manageData" @updateTeachPlan="updateTeachPlan"/>

        </div>

      </div>
  </a-spin>
</template>

<script lang="ts" setup>
import { AiEditor } from "aieditor"
import "aieditor/dist/style.css"
import { ref, onMounted, onUnmounted, nextTick} from "vue"
import AiEditors from "@/components/lessonPlan/AiEditors.vue";
import { useRoute, useRouter } from 'vue-router'
import { saveTeachPlan } from '@/services/api/LessonPlan'
import { ElMessage } from 'element-plus'

const teachPlanData = ref({
  desc: window.history.state.desc as string ,
  template: window.history.state.template as string
})

const manageData = ref({
  content: window.history.state.content as string
})


const spinning = ref(false)

const router = useRouter()
const handleBack = () => {
  router.back()
}

const teachPlanItems = ref('');
const updateTeachPlan = (teachPlan: any) => {
  // console.log("收到子组件传来的教案:", teachPlan)
  teachPlanItems.value = teachPlan
  const match = teachPlanItems.value.match(/^# (.+)$/m);
  title.value = match ? match[1] : '无标题';
}

const title = ref('这是一个可编辑标题')
const teachPlanTitle = ref('')
const editing = ref(false)
const inputRef = ref<HTMLInputElement | null>(null)

function startEditing() {
  teachPlanTitle.value = title.value
  editing.value = true
  // 等 DOM 渲染完成后聚焦
  nextTick(() => {
    inputRef.value?.focus()
  })
}

function saveTitle() {
  title.value = teachPlanTitle.value.trim() || title.value
  editing.value = false
}


const currentTime = ref('')

// 格式化当前时间为 YYYY-MM-DD HH:mm:ss
function formatTime(date: Date): string {
  const Y = date.getFullYear()
  const M = String(date.getMonth() + 1).padStart(2, '0')
  const D = String(date.getDate()).padStart(2, '0')
  const h = String(date.getHours()).padStart(2, '0')
  const m = String(date.getMinutes()).padStart(2, '0')
  const s = String(date.getSeconds()).padStart(2, '0')
  return `${Y}-${M}-${D} ${h}:${m}:${s}`
}


const savePlan = async() => {

  const content = teachPlanItems.value

  const params = {
      title: title.value,
      content: content,
      chapter_id: 12
}
  try {
    spinning.value = true
    const res = await saveTeachPlan(params)
    console.log(res);
    spinning.value = false //加载
    ElMessage.success('保存成功！')//保存成功提示
    currentTime.value = formatTime(new Date())
  } catch (error) {
    console.error('保存失败:', error)
  }
}

</script>

<style scoped>
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 6vh;
  background-color: #ffffff;
  box-sizing: border-box;
}

.topTitle{
  width: 300px;
  font-size: 1vw;
  font-weight: bold;
  white-space: nowrap;      /* 不换行 */
  overflow-x: auto;         /* 超出时显示横向滚动条 */
}

.left,
.right {
  height: 6vh;
  display: flex;
  align-items: center;
  margin-right: 1vw;
}

.back-btn {
  cursor: pointer;
  margin: 0 2vw;
  font-size: 14px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 6px;
}

.date {
  margin-left: 2vw;
  color: gray;
}

.btn1{
  width: 7vw;
  height: 4vh;
  background: white;
  color: rgba(102, 102, 102, 1);
  font-weight: 500;
  padding: 0.5vh 0.8vw;
  border-radius: 2vw;
  border: 0.1vw solid rgba(229, 229, 229, 1);
  margin-left: 1vw;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.btn2{
  width: 10vw;
  height: 5vh;
  background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
  color: white;
  font-weight: bold;
  padding: 0.5vh 0.8vw;
  border-radius: 2vw;
  border: none;
  margin-left: 1vw;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.page-content {
  height: 100%;
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: cover;
  box-sizing: border-box;
  /* padding: 2vw 0 0 0; */
  display: flex;
  justify-content:space-between;
  gap:2vw
}

</style>
