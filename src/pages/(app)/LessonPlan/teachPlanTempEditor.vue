<template>
  <a-spin :spinning="spinning">
    <div class="page">

      <!-- 顶部返回 -->
      <div class="page-header">
        <div class="left">
          <div @click="handleBack" class="back-btn">
            <img style="width: 14px;height: 10px;" src="@/assets/image/ArrowLeft.svg"/>
            <span class="back-text">返回</span>
          </div>
                <span v-if="!editing" @click="startEditing" class="topTitle">{{ title }}</span>
                <input
                  v-else
                  v-model="teachPlanTitle"
                  @blur="saveTitle"
                  ref="inputRef"
                  class="topTitle"
                />

            <div class="date">已保存于 {{ currentTime }}</div>
        </div>
        <div class="right">
          <button class="btn1" @click="saveTemp()">保存加关联</button>
          <button class="btn2" @click="afterSelect()">下一步-输入描述</button>
        </div>
      </div>

      <div class="page-content">
        <!-- 教案编辑区 -->
        <div class="editor">
          <div v-html="parseMarkdown(templateItems[selectedIndex])"></div>
        </div>
        <!-- 模板选择区 -->
        <div class="template-panel">
          <div class="toolbar">
            <button class="toolbar-button">全部</button>
            <button class="toolbar-button">系统内置</button>
            <button class="toolbar-button">课程1</button>
            <button class="toolbar-button">课程2</button>
          </div>
          <div class="template-grid">
            <div
              v-for="(template, index) in templateItems"
              :key="index"
              :class="['template-item', selectedIndex === index ? 'selected' : '']"
              @click="selectTemplate(index)"
            >
              <div class="template-index" :class="{ active: selectedIndex === index }">
                {{ (index + 1).toString().padStart(2, '0') }}
              </div>
              <div class="template-card">
                <div class="markdown-body" v-html="parseMarkdown(template)"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </a-spin>
</template>

<script lang="ts" setup>
import { ref,onMounted,nextTick} from 'vue'
import { useRouter } from 'vue-router'
import { teachPlanTempList, saveTeachPlanTemp } from '@/services/api/LessonPlan'
import { parseMarkdown } from "@/utils/markdownParser";
import { ElMessage } from 'element-plus'

const spinning = ref(false)

const router = useRouter()
const handleBack = () => {
  router.back()
}

const title = ref('这是一个可编辑标题')
const teachPlanTitle = ref('')
const editing = ref(false)
const inputRef = ref<HTMLInputElement | null>(null)

function startEditing() {
  teachPlanTitle.value = title.value
  editing.value = true
  // 等 DOM 渲染完成后聚焦
  nextTick(() => {
    inputRef.value?.focus()
  })
}

function saveTitle() {
  title.value = teachPlanTitle.value.trim() || title.value
  editing.value = false
}


const currentTime = ref('')

// 格式化当前时间为 YYYY-MM-DD HH:mm:ss
function formatTime(date: Date): string {
  const Y = date.getFullYear()
  const M = String(date.getMonth() + 1).padStart(2, '0')
  const D = String(date.getDate()).padStart(2, '0')
  const h = String(date.getHours()).padStart(2, '0')
  const m = String(date.getMinutes()).padStart(2, '0')
  const s = String(date.getSeconds()).padStart(2, '0')
  return `${Y}-${M}-${D} ${h}:${m}:${s}`
}

const templateItems = ref([""])

onMounted(() => {
  getTeachPlanTempList()
})

// 获取教案列表
async function getTeachPlanTempList () {
  const params = {
    page: 1,
    pageSize: 10,
    is_deleted:'false'
  }
  try {
    spinning.value = false
    const res = await teachPlanTempList(params)

    for(const item of res.data.results){
      templateItems.value.push(item.content)
    }
    // templateItems.value.shift() // 移除第一个空字符串

    // 移除空字符串和null值
    templateItems.value = templateItems.value.filter(item => item !== "" && item !== null && item !== undefined)
    content.value = templateItems.value[selectedIndex.value]
    const match = content.value.match(/^# (.+)$/m);
    title.value = match ? match[1] : '无标题';

    spinning.value = false
  } catch (error) {
    console.error('获取模板列表失败:', error)
  }
}

const selectedIndex = ref(0)
const content = ref('')
// const content = templateItems.value[selectedIndex.value]

function selectTemplate(index: number) {
  selectedIndex.value = index
  content.value = templateItems.value[selectedIndex.value]
  const match = content.value.match(/^# (.+)$/m);
  title.value = match ? match[1] : '无标题';
}

const saveTemp = async() => {
  const params = {
      title: title.value,
      content: content.value,
      course_id: 12
  }
  try {
    spinning.value = true
    const res = await saveTeachPlanTemp(params)
    spinning.value = false
    ElMessage.success('保存成功！')
    currentTime.value = formatTime(new Date())
  } catch (error) {
    console.error('获取PPT列表失败:', error)
  }
}

const afterSelect = () => {
  router.push({
      path:'/LessonPlan/afterSelectTemp',
      state: {
        content: templateItems.value[selectedIndex.value]
      }
    })
}



</script>

<style scoped>
.page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 6vh;
  background-color: #ffffff;
  box-sizing: border-box;
}


.left,
.right {
  height: 6vh;
  display: flex;
  align-items: center;
  margin-right: 1vw;
}

.back-btn {
  cursor: pointer;
  margin: 0 2vw;
  font-size: 14px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 6px;
}

.date {
  margin-left: 2vw;
  color: gray;
}

.btn1{
  width: 7vw;
  height: 4vh;
  background: white;
  color: rgba(102, 102, 102, 1);
  font-weight: 500;
  padding: 0.5vh 0.8vw;
  border-radius: 2vw;
  border: 0.1vw solid rgba(229, 229, 229, 1);
  margin-left: 1vw;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.btn2{
  width: 10vw;
  height: 4vh;
  background: linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%);
  color: white;
  font-weight: bold;
  padding: 0.5vh 0.8vw;
  border-radius: 2vw;
  border: none;
  margin-left: 1vw;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}


.page-content {
  height: 100%;
  background: url(@/assets/image/bg1.png) no-repeat center top;
  background-size: cover;
  box-sizing: border-box;
  padding: 2vw 10vw;
  display: flex;
  gap:2vw;
}

.editor {
  width: 60%;
  height: 90%;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: auto;
}

.template-panel {
  flex: 1;
  height: 90%;
  background: white;
  padding: 16px;
  border-radius: 8px;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.toolbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 1vw;
  border-bottom: 0.1vw solid rgba(229, 229, 229, 1);
}

.toolbar-button{
  width: 5vw;
  color: #3f8cff;
  border: 0.1vw solid #3f8cff;
  padding: 0.6vh 0.8vw;
  font-size: 0.8vw;
  font-weight: 500;
  border-radius: 0.4vw;
  background: rgba(255, 255, 255, 1);
  cursor: pointer;
}
.toolbar-button:hover {
  background:#3f8cff;
  color: #ffffff;
}


.template-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  overflow: auto;
  padding-top: 1vw;
}

.template-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}

.template-index {
  font-weight: bold;
  font-size: 18px;
  color: #666;
  width: 28px;
  flex-shrink: 0;
}

.template-index.active {
  color: #409EFF;
}

.template-card {
  flex: 1;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 12px 16px;
  padding: rgba(201, 223, 255, 1);
  font-size: 14px;
  color: #333;
  line-height: 1.8;
  white-space: nowrap;
  box-sizing: border-box;
  transition: all 0.3s;
}

.template-item.selected .template-card {
  background-color: #f5faff;
}

.markdown-body {
  font-size: 0.1vh;
  line-height: 0.4vh;
  color: #333;
}

</style>
