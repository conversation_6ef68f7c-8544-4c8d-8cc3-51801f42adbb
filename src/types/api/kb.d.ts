declare namespace API {
  declare namespace Kb {
    interface File {
      chunk_num: number
      create_date: string
      create_time: number
      created_by: string
      id: string
      kb_id: string
      location: string
      name: string
      parser_config: ParserConfig
      parser_id: string
      process_begin_at?: string
      process_duation: number
      progress: number
      progress_msg: string
      run: DocumentRunningStatus
      size: number
      source_type: string
      status: string
      thumbnail: string
      token_num: number
      type: string
      update_date: string
      update_time: number
      meta_fields?: Record<string, unknown>
    }

    interface ParserConfig {
      chunk_token_num?: number
      delimiter?: string
      html4excel?: boolean
      layout_recognize?: 'DeepDoc' | 'Plain Text'
      pages?: [number, number][]
      auto_keywords?: number
      auto_questions?: number
      raptor?: {
        use_raptor: boolean
      }
      graphrag?: GraphRag
    }

    interface GraphRag {
      community?: boolean
      entity_types?: string[]
      method?: string
      resolution?: boolean
      use_graphrag?: boolean
    }

    interface Chunk {
      available_int: number // Whether to enable, 0: not enabled, 1: enabled
      chunk_id: string
      content_with_weight: string
      doc_id: string
      doc_name: string
      img_id: string
      important_kwd?: string[]
      question_kwd?: string[] // keywords
      tag_kwd?: string[]
      positions: number[][]
      tag_feas?: Record<string, number>
    }

    enum DocumentRunningStatus {
      UNSTART = '0', // need to run
      RUNNING = '1', // need to cancel
      CANCEL = '2', // need to refresh
      DONE = '3', // need to refresh
      FAIL = '4', // need to refresh
    }
  }
}
