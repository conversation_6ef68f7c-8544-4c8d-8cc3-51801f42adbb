
.search-icon{
    cursor: pointer;
}
.container {
    padding: 20px;
    
    border-radius: 14px;
    width: 70%;
}
.flexInit{
    display: flex;
}

.flex{
    display: flex;
    align-items: center;
}
.flex-s-between{
    justify-content: space-between;
}
.detele{
    flex-shrink: 0;
}
.detele:hover{
    cursor: pointer;
}
.table-header {
    background-color: #f4f1f1 !important;
    color: #000;
    font-weight: bold;
    display: flex;
    align-items: center;
    padding: 0 20px;
    margin-top: 20px;
}

.com-cell {
    font-weight: 600;
    text-align: left;
}

.com-cell-d {
    text-align: left;
    padding: 4px 0;

}

.cell-1-0 {
    width: 60px;
    text-align: left;
}

.cell-1-1 {
    width: 100px;
}

.cell-1-2 {
    flex: 1;
    min-width: 140px;
    padding: 0 20px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
    -moz-line-clamp: 5;
    -ms-line-clamp: 5;
    -o-line-clamp: 5;
    line-clamp: 5;

    overflow: hidden;
}

.cell-1-3 {
    width: 120px;
}

.cell-1-4 {
    width: 100px;
}

.cell-1-5 {
    width: 100px;
}

.cell-1-6 {
    text-align: center;
    width: 120px;
}

.tbodyTr {
    cursor: pointer;
    border-bottom: #efefef 1px solid;
    padding: 6px 20px;
}

.tbodyTr:hover {
    border: #36aadf 1px solid;
    background-color: #fff;
    border-radius: 3px;
}

.tbodyTr-1 {
    background-color: #f7f7f7;
}

.detailsQ-title{
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0 16px 0;
    padding: 0 0 0 10px;
    border-left: #36aadf 4px solid;
    line-height: 16px;
}
.question-details{
    padding: 24px;
    margin: 16px 0 20px 0;
    background-color: #fafafa;
    border-radius: 10px;
    
    .questionType{
        color: rgb(0, 154, 255);
        border: 1px solid rgb(0, 154, 255);
        font-size: 12px;
        border-radius: 6px;
        display: inline-block;
        margin: 3px 4px 0 0;
        padding: 0 4px;
        flex-shrink: 0;
        height: 22px;
    }
    .question-text{
        display: inline-block;

    }
    .question-answer{
        display: flex;
        font-size: 14px;
        color: #009aff;
        margin: 10px 0;
    }
    .question-explanation{
        font-size: 14px;
    }
    .briVerti{
        float: left;
        height: 27px;
        line-height: 22px;
        margin-right: 6px;
        margin-left: 5px;
        color: #b8b8b8;
    }
}