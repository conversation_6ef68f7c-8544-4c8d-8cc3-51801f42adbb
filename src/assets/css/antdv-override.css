@layer components {
  .gradient-a-button {
    @apply border-none!;

    &:not(*:disabled) {
      background-image: linear-gradient(135.84deg, #219fffff 0%, #0066ffff 100%) !important;
      &:hover {
        background-image:
          linear-gradient(0deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.3)),
          linear-gradient(135.84deg, rgba(33, 159, 255, 1) 0%, rgba(0, 102, 255, 1) 100%) !important;
      }
    }
  }

  .outline-a-button {
    @apply bg-white!;

    &:not(*:disabled) {
      &:hover {
        background-color: #d1e3ffff !important;
      }
    }
  }

  .reset-ant-table-pagination {
    .ant-pagination {
      @apply my-5! space-x-2! **:border-none!;

      .ant-pagination-item-link {
        @apply rounded-xs!;
      }

      .ant-pagination-item {
        @apply rounded-xs!;

        a {
          @apply flex! size-6! items-center justify-center;
        }

        &:not(.ant-pagination-item-active) {
          a {
            @apply text-foreground-3!;
          }
        }
        &.ant-pagination-item-active {
          @apply bg-primary!;
          a {
            @apply text-white!;
          }
        }
      }
    }
  }
}
