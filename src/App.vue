<script setup lang="ts">
  import type { AliasToken } from 'ant-design-vue/es/theme/internal'
  import zhCN from 'ant-design-vue/es/locale/zh_CN'

  const antdThemeToken: Partial<AliasToken> = {
    colorPrimary: '#3f8cff',
  }
</script>

<template>
  <AConfigProvider
    :theme="{ token: antdThemeToken }"
    :locale="zhCN"
  >
    <AApp>
      <!-- 允许路由组件使用 async setup -->
      <RouterView v-slot="{ Component }">
        <Suspense>
          <component :is="Component" />

          <template #fallback>
            <!-- TODO: better loading -->
            <div class="flex h-screen items-center justify-center">loading...</div>
          </template>
        </Suspense>
      </RouterView>
    </AApp>
  </AConfigProvider>
</template>
