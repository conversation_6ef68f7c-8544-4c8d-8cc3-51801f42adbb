import http from "@/services/http";

interface LessonPlanData {
    topic: string;  // 只传入 topic 字段
}

const LessonPlanGenerate = {
    generate(data:LessonPlanData) {
        return http({
            url: '/api/v1/lesson_tmp/lessons/',
            method: "post",
            //headers: { "Content-Type": "application/json" },
            data: data
        })
    }
};

export default LessonPlanGenerate;


//教案列表
// api/v1/teachplan/?page=1&page_size=10&is_deleted=false
export const teachPlanList = (params: { pageSize: number, page: number, is_deleted: string }) => {
    return http({
        url: `api/v1/teachplan/?page_size=${params.pageSize}&page=${params.page}&is_deleted=${params.is_deleted}`,
        method: "get",
    })
}

//教案软删除
// api/v1/teachplan/soft_delete/
export const teachPlan_softDelete = (params: any) => {
    return http({
        url: `api/v1/teachplan/soft_delete/`,
        method: "post",
        data: params
    })
}

//教案硬删除
// api/v1/teachplan/hard_delete/
export const teachPlan_hardDelete = (params: any) => {
    return http({
        url: `api/v1/teachplan/hard_delete/`,
        method: "post",
        data: params
    })
}

//上传教案
// api/v1/teachplan/upload_file/
export const teachPlanUpload = (params: any) => {
    return http({
        url: `api/v1/teachplan/upload_file/`,
        headers: {
            "Content-Type": "multipart/form-data",
        },
        method: "post",
        data: params
    })
}


//教案模板列表
// api/v1/teachplanTemplate/?page=1&page_size=10&is_deleted=false
export const teachPlanTempList = (params: { pageSize: number, page: number, is_deleted: string }) => {
    return http({
        url: `api/v1/teachplanTemplate/?page_size=${params.pageSize}&page=${params.page}&is_deleted=${params.is_deleted}`,
        method: "get",
    })
}

//教案模板软删除
// api/v1/teachplanTemplate/soft_delete/
export const teachPlanTemp_softDelete = (params: any) => {
    return http({
        url: `api/v1/teachplanTemplate/soft_delete/`,
        method: "post",
        data: params
    })
}

//教案模板硬删除
// api/v1/teachplanTemplate/hard-delete/
export const teachPlanTemp_hardDelete = (params: any) => {
    return http({
        url: `api/v1/teachplanTemplate/hard-delete/`,
        method: "post",
        data: params
    })
}

//上传教案模板
// api/v1/teachplanTemplate/upload_file/
export const teachPlanTempUpload = (params: any) => {
    return http({
        url: `api/v1/teachplanTemplate/upload_file/`,
        headers: {
            "Content-Type": "multipart/form-data",
        },
        method: "post",
        data: params
    })
}

//教案模板生成
export const getTeachPlanTemp = (params:any) =>{
    return fetch(`/api/api/v1/teachplanTemplate/get_teachplan_template/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        theme:params.theme,
        document:params.document
      }),
    })
}

//教案模板大纲保存
// api/v1/teachplanTemplate/save_teachplan_template/
export const saveTeachPlanTemp = (params:any) => {
    return http({
        url: 'api/v1/teachplanTemplate/save_teachplan_template/',
        method: "post",
        data: params
    })
}

//教案生成
// api/v1/teachplan/lessons/
export const getTeachPlan = (teachPlanParams: any) =>{
    return fetch(`/api/api/v1/teachplan/lessons/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(teachPlanParams),
    })
}

//保存教案
// api/v1/teachplan/save_teachplan/
export const saveTeachPlan = (params:any) => {
    return http({
        url: 'api/v1/teachplan/save_teachplan/',
        method: "post",
        data: params
    })
}

//ai对话
export const aiConversation = (theme: string) =>{
    return fetch(`/api/api/v1/teachplan/conversation/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        "user_input" : theme
      }),
    })
}

