<template>
    <div class="">
        <div>
            <div class="group-title">
                多选题 - 试题题干
            </div>
        </div>
        <div class="options-item">
            <div class="aiEditorClass" v-if="params.text || props.isType == 'add' ">
                <QuestionEditor :questionValue="params.text" :indexNumber="'text'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div class="aiEditorClass" v-else>
                <QuestionEditor :questionValue="params.text" :indexNumber="'text'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div style="margin: 0 0 0 10px;"><el-icon :size="20" color="#ff0000"> </el-icon></div>
        </div>
        <div class="group-title" style="margin-top: 24px;">
            答案选项
        </div>
        <div>
            <div v-for="(item, index) in params.options" :key="index">
                <div style="margin: 10px 0;">选项 {{ getEn(index) }}</div>
                <div class="options-item">
                    <div class="aiEditorClass">
                        <QuestionEditor :key="item.key" :questionValue="item.value" :indexNumber="index"
                            @valueEditorChange="getText">
                        </QuestionEditor>
                    </div>
                    <div style="margin: 0 0 0 10px;cursor: pointer;">
                        <el-icon :size="20" color="#ff0000" @click="deleteQuestion(index)">
                            <Delete />
                        </el-icon>
                    </div>
                </div>
            </div>
        </div>

        <div>
            <el-button type="primary" @click="addQuestion" size="large" style="margin-top: 24px;">添加新选项</el-button>
        </div>

        <div style="margin: 24px 0 10px 0;" class="group-title">正确答案:</div>
        <div>
            <el-checkbox-group v-model="params.answer">
                <el-checkbox :value="getEn(index)" size="large" border v-for="(item, index) in params.options"
                    :key="index">
                    <div style="padding: 0 0px 0 20px;font-size: 20px;">{{ getEn(index) }}</div>
                </el-checkbox>
            </el-checkbox-group>
        </div>

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                试题知识点
            </div>
        </div>
        <div class="options-item">
            <el-input v-model="params.knowledge_point" style="width:400px" :rows="4" type="textarea"
                placeholder="请输入试题知识点" />
        </div>

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                试题难度
            </div>
        </div>
        <div class="options-item">
            <el-select v-model="params.difficulty" placeholder="请选择试题难度" size="large" style="width: 240px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </div>

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                试题解析
            </div>
        </div>
        <div class="options-item">
            <div class="aiEditorClass" v-if="params.explanation || props.isType == 'add' ">
                <QuestionEditor :questionValue="params.explanation" :indexNumber="'explanation'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div class="aiEditorClass" v-else>
                <QuestionEditor :questionValue="params.explanation" :indexNumber="'explanation'" @valueEditorChange="getText"></QuestionEditor>
            </div>
            <div style="margin: 0 0 0 10px;"><el-icon :size="20" color="#ff0000"> </el-icon></div>
        </div>

        <div style="margin-top: 44px;">
            <div class="group-titlef">
                试题标签
            </div>
        </div>
        <div class="options-item" style="align-items: flex-end;">
            <div style="" class="lable">
                标签1
            </div>
            <div style="margin: 0 30px 0 30px;">
                <el-button size="large" type="primary" plain>添加标签</el-button>
            </div>
        </div>

        <el-button type="primary" @click="saveQuestion" size="large" :loading="loadingButton" style="margin: 54px 0;">保存试题</el-button>

    </div>
</template>
<script lang="ts" setup>
import QuestionEditor from "@/components/QuestionEditor.vue";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { v4 as uuidv4 } from 'uuid'; // 导入uuid库
import { addMultipleQuestions, editMultipleQuestions } from '@/services/api/question'
import { openMessage,getEn } from '@/utils/util'
import { useRouter } from 'vue-router';

const loadingButton = ref(false);

const router = useRouter();
// 定义数据类型
interface EditorData {
    indexNumber: string | number;
    valueEditor: string;
}

const props = defineProps({
    questionDetails: {
        type: Object,
        required: true
    },
    isType: {
        tye: String,
        required: false
    }
})

const options = [
    {
        value: 0,
        label: '简单',
    },
    {
        value: 1,
        label: '中等',
    },
    {
        value: 2,
        label: '困难',
    }
]


// 默认值
const defaultParams = {
    text: "",
    difficulty: 0,  // 难度
    knowledge_point: "",  // 知识点
    label_id: 1,  // 标签
    options: [
        { value: "", key: uuidv4() },
        { value: "", key: uuidv4() },
        { value: "", key: uuidv4() },
        { value: "", key: uuidv4() }
    ],
    answer: [],
    explanation: '' // 解析
};

// 响应式对象
const params = ref({ ...defaultParams });

onMounted(() => {
    if (props.questionDetails) {
        params.value = { ...defaultParams, ...props.questionDetails }; // 深度合并默认值和传入的值
    }
})


function addQuestion() {
    params.value.options.push({ value: "", key: uuidv4() })
}
//获取题干文本数据
function getText(data: EditorData) {
    if (data.indexNumber == 'text') {
        params.value.text = data.valueEditor
    } else if (typeof data.indexNumber == 'number') {
        params.value.options[data.indexNumber].value = `${data.valueEditor}`// ${getEn(data.indexNumber)}.${' '}
    } else if (data.indexNumber == 'explanation') {
        params.value.explanation = data.valueEditor
    }
}

//删除选项
function deleteQuestion(index: number) {
    params.value.options.splice(index, 1);
}



//保存
function saveQuestion() {
    // const optionsToSend = params.value.options.map((option,index) => `${getEn(index)}.${' '}${option.value}`);
    const hasEmptyOption = params.value.options.some(option => option.value.trim() === '');


    if (params.value.text == '') {
        openMessage('请输入题干', 'error')
        return
    }
    if (hasEmptyOption) {
        openMessage('请输入全部选项', 'error');
        return;
    }
    if (params.value.answer.length == 0) {
        openMessage('请选择答案', 'error')
        return
    }
    console.log(params.value, 'params.value');
    loadingButton.value = true;


    if(props.isType == 'edit'){
        console.log('修改试题')
        editMultipleQuestions(params.value).then(res => {
            console.log(res);
            router.go(-1);
            loadingButton.value = false;
            openMessage('保存成功', 'success')
            // setTimeout(() => {
            //     resetForm();
            //     window.location.reload();
            // },1000)   // 刷新页面
        })
    }else if(props.isType == 'add'){
        console.log('新增试题')
        addMultipleQuestions(params.value).then(res => {
            // console.log(res, 'res');
            openMessage('保存成功', 'success')
            loadingButton.value = false
            setTimeout(() => {
                resetForm();
                // window.location.reload();
            },1000)   // 刷新页面
        })
    }

}
function resetForm() {
    params.value.text = ''
    params.value.difficulty = 0
    params.value.knowledge_point = ''
    params.value.label_id = 1
    params.value.options = [
        { value: "", key: uuidv4() },
        { value: "", key: uuidv4() },
        { value: "", key: uuidv4() },
        { value: "", key: uuidv4() }
    ]
    params.value.answer = []
    params.value.explanation = ''
}


</script>
<style lang="scss" scoped>
@import url('@/assets/css/questioncss/selectCss.css');
</style>
