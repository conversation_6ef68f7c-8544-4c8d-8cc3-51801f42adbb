<template>
    <div class="question-details">
        <div class="flexInit flex-s-between">
            <div class="flexInit">
                <text class="questionType">{{ questionItem.type }}</text>
                <span class="question-text" v-html="parseMarkdown(questionItem.text)"></span>
            </div>
            <div class="detele flexInit">
                <div @click="deleteQuestions(questionItem)"><el-icon :size="18" color="#409efc" ><Delete /></el-icon></div>
                <!-- <div>删除</div> -->
            </div>
        </div>
        <div v-for="(option, index) in questionItem.options" :key="index" style="margin: 6px;" class="flex">
            {{ getEn(Number(index)) }}、
            <text v-html="parseMarkdown(option.value) || option"></text>
        </div>
        <div class="question-answer">
            <div style="flex-shrink: 0;">正确答案：</div>
            <div v-if="Array.isArray(questionItem.answer)">
                <text v-for="(answer, index) in questionItem.answer" :key="index">
                    {{ Object.prototype.toString.call(answer) === '[object Object]' ? answer.blank + ' ' : answer }}
                </text>
            </div>
            <div v-else>
                <text v-html="parseMarkdown(questionItem.answer)"></text>
            </div>
        </div>

        <div class="flexInit" style="font-size: 14px;">
            <div style="flex-shrink: 0">知识点：</div>
            <span>{{ questionItem.knowledge_point || '暂无知识点' }}</span>
        </div>
        <div class="flexInit" style="margin: 10px 0;">
            <div style="flex-shrink: 0;font-size: 14px;">题目解析：</div>
            <div class="question-explanation" v-html="parseMarkdown(questionItem.explanation)"></div>
        </div>

        <div class="flex" style="color: #999;">
            <div class="question-explanation">标签：{{ questionItem.label_id }}</div>
            <span class="briVerti">|</span>
            <div class="question-explanation">难度：{{ levelTransform(Number(questionItem.difficulty)) }}</div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { parseMarkdown } from '@/utils/markdownParser'; // 引入工具函数
import { openMessage, openMessageBox, getEn, levelTransform, questionTypeData} from "@/utils/util";
// export default {
//   name: 'QuestionDetailsCom'
// }


const props = defineProps({
    questionItem: {
        type: Object,
        required: true
    }
})

function deleteQuestions( questionItem: any) {
    console.log(questionItem,'----')
}



</script>

<style lang="scss" scoped>
@import url('@/assets/css/questioncss/questionList.css');
</style>
