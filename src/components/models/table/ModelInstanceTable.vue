<script lang="ts">
  import type { ColumnType } from 'ant-design-vue/es/table'

  export interface TableItem {
    id: string
    name: string
    status: API.Models.ModelInstance['state']
    createTime: Date
  }

  const columns = [
    {
      title: '实例名称',
      dataIndex: 'name',
      key: 'name',
      width: 420,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 160,
    },
    {
      title: '创建时间',
      key: 'createTime',
      customRender: ({ record }) => record.createTime.toLocaleString('zh-CN'),
    },
  ] as const satisfies ColumnType<TableItem>[]

  const tableData: TableItem[] = Array.from({ length: 20 }, (_, i) => ({
    id: `instance-${i}`,
    name: `模型实例 ${i}`,
    status: ['running', 'initializing', 'error'][i % 3] as API.Models.ModelInstance['state'],
    createTime: new Date(Date.now() - i * 1000 * 60 * 60),
  }))
</script>

<script setup lang="ts">
  console.log('model instance table')
</script>

<template>
  <ATable
    :columns="columns"
    :data-source="tableData"
    class="[&_.ant-table]:ml-16! **:[th]:border-t-0!"
    :pagination="{ size: 'small' }"
  >
    <template
      #bodyCell="// @ts-expect-error antdv poor typing
      { column, record }: { record: TableItem; column: (typeof columns)[number] }"
    >
      <template v-if="column.key === 'status'">
        <ModelInstanceStatusTag :status="record.status" />
      </template>
    </template>
  </ATable>
</template>
