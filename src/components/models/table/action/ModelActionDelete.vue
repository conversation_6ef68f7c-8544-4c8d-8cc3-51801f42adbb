<script setup lang="ts">
  import type { TableItem } from '../ModelTable.vue'

  import IconTrash from '~icons/lucide/trash-2'

  type Props = {
    model: TableItem
  }
  const props = defineProps<Props>()
</script>

<template>
  <AButton
    type="text"
    size="small"
    class="size-auto! p-1!"
    title="删除"
    @click="console.log('delete')"
  >
    <IconTrash class="text-primary" />
  </AButton>
</template>
