<script setup lang="ts">
  import { injectModelsContext } from '@/utils/context/models'
  import type { TableItem } from '../ModelTable.vue'

  import IconEdit from '~icons/local/pencil-square'

  type Props = {
    model: TableItem
  }
  const props = defineProps<Props>()

  const { setDeployFormInitialValues, setIsDeployModalOpen, setFormAction } = injectModelsContext()

  function handleEdit() {
    setDeployFormInitialValues(props.model)
    setFormAction('update')
    setIsDeployModalOpen(true)
  }
</script>

<template>
  <AButton
    type="text"
    size="small"
    class="-ml-1! size-auto! p-1!"
    title="编辑"
    @click="handleEdit"
  >
    <IconEdit class="text-primary" />
  </AButton>
</template>
