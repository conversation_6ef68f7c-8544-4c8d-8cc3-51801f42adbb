<script setup lang="ts">
  import type { TableItem } from '../ModelTable.vue'

  import IconPlay from '~icons/lucide/circle-play'

  type Props = {
    model: TableItem
  }
  const props = defineProps<Props>()
</script>

<template>
  <AButton
    type="text"
    size="small"
    class="size-auto! p-1!"
    title="运行"
    @click="console.log('run')"
  >
    <IconPlay class="text-primary" />
  </AButton>
</template>
