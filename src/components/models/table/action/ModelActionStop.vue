<script setup lang="ts">
  import type { TableItem } from '../ModelTable.vue'

  import IconStop from '~icons/lucide/circle-stop'

  type Props = {
    model: TableItem
  }
  const props = defineProps<Props>()
</script>

<template>
  <AButton
    type="text"
    size="small"
    class="size-auto! p-1!"
    title="停止"
    @click="console.log('stop')"
  >
    <IconStop class="text-primary" />
  </AButton>
</template>
