<script setup lang="ts">
  interface Props {
    status: API.Models.ModelInstance['state']
  }

  const props = defineProps<Props>()

  const colorMap = {
    initializing: 'processing',
    pending: 'warning',
    starting: 'processing',
    running: 'success',
    scheduled: 'default',
    error: 'error',
    downloading: 'processing',
    analyzing: 'processing',
    unreachable: 'error',
  } as const

  const statusColor = computed(() => colorMap[props.status] || 'default')
</script>

<template>
  <ATag :color="statusColor">
    {{ props.status }}
  </ATag>
</template>
