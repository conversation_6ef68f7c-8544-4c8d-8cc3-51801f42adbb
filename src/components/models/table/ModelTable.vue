<script lang="ts">
  import type { ColumnType } from 'ant-design-vue/es/table'

  import type { FormValues } from '../ModelDeployForm.vue'

  export interface TableItem extends FormValues {
    id: string
    name: string
    source: string
    replicaNum: number
    activeReplicaNum: number
    createTime: Date
  }

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      width: 240,
    },
    {
      title: '副本数',
      key: 'replicas',
      customRender: ({ record }) => `${record.activeReplicaNum} / ${record.replicaNum}`,
      width: 160,
    },
    {
      title: '创建时间',
      key: 'createTime',
      customRender: ({ record }) => record.createTime.toLocaleDateString('zh-CN'),
    },
    {
      title: '操作',
      key: 'action',
    },
  ] as const satisfies ColumnType<TableItem>[]
</script>

<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'

  import { injectModelsContext } from '@/utils/context/models'

  const props = defineProps<{
    models: TableItem[]
  }>()

  const { selectedModels, setSelectedModels } = injectModelsContext()
</script>

<template>
  <ATable
    :class="
      twMerge(
        'reset-ant-table-pagination',
        '**:[.ant-table]:bg-transparent! **:[td,th]:border-[#CEDFF2FF]!',
        '**:[th]:rounded-none! **:[th]:border-t! **:[th]:bg-transparent! **:[th]:before:hidden',
        '**:[.ant-table-cell.ant-table-cell-row-hover]:bg-foreground-1/3! **:[.ant-table-cell]:bg-transparent!',
      )
    "
    :columns="columns"
    :data-source="props.models"
    :pagination="{ size: 'small' }"
    :scroll="{ x: 1000 }"
    :row-key="(record: TableItem) => record.id"
    :row-selection="{
      selectedRowKeys: selectedModels,
      onChange(selectedRowKeys) {
        setSelectedModels(selectedRowKeys as string[])
      },
    }"
    :row-expandable="(record: TableItem) => record.replicaNum > 0"
  >
    <template
      #bodyCell="// @ts-expect-error antdv poor typing
      { column, record, }: { record: TableItem; column: (typeof columns)[number] }"
    >
      <div
        v-if="column.key === 'action'"
        class="flex items-center space-x-2"
      >
        <ModelActionEdit :model="record" />
        <ModelActionDelete :model="record" />
        <ModelActionStop
          v-if="record.activeReplicaNum > 0"
          :model="record"
        />
        <ModelActionRun
          v-else
          :model="record"
        />
      </div>
    </template>

    <template #expandedRowRender="{ record }">
      <ModelInstanceTable :model-id="record.id" />
    </template>
  </ATable>
</template>
