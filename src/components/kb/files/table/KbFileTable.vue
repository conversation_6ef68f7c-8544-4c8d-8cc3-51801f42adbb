<script lang="ts">
  import type { ColumnType } from 'ant-design-vue/es/table'

  export type TableItem = {
    id: string
    name: string
    chunkNum: number
    createTime: Date
    chunkMethod: API.Kb.File['parser_id']
    chunkStatus: 'complete' | 'running' | 'failed' | 'not-started'
  }

  export const columns = [
    {
      title: '名称',
      key: 'name',
      width: 240,
    },
    {
      title: '分块数',
      dataIndex: 'chunkNum',
      key: 'chunkNum',
      width: 120,
    },
    {
      title: '创建时间',
      key: 'createTime',
      customRender: ({ record }) => record.createTime.toLocaleString('zh-CN'),
    },
    {
      title: '分块方法',
      dataIndex: 'chunkMethod',
      key: 'chunkMethod',
      width: 120,
    },
    {
      title: '解析状态',
      dataIndex: 'chunkStatus',
      key: 'chunkStatus',
      width: 200,
    },
    {
      title: '操作',
      key: 'action',
    },
  ] as const satisfies ColumnType<TableItem>[]
</script>

<script setup lang="ts">
  import { twMerge } from 'tailwind-merge'

  import { injectKbFilesContext } from '@/utils/context/kb'
  import KbFileName from './KbFileName.vue'

  const props = defineProps<{
    files: TableItem[]
  }>()

  const { selectedKbs, setSelectedKbs } = injectKbFilesContext()
</script>

<template>
  <ATable
    :data-source="props.files"
    :columns="columns"
    :class="
      twMerge(
        'reset-ant-table-pagination',
        'rounded-[5px] bg-white px-5 pb-2.5',
        '[&_.ant-table-thead_.ant-table-cell]:bg-white! [&_.ant-table-thead_.ant-table-cell]:before:hidden',
      )
    "
    :row-selection="{
      selectedRowKeys: selectedKbs,
      onChange(selectedRowKeys) {
        setSelectedKbs(selectedRowKeys as string[])
      },
    }"
    :pagination="{ size: 'small' }"
    :row-key="(record) => record.id"
  >
    <template
      #bodyCell="//@ts-expect-error antdv poor typing
      { column, record }: { record: TableItem; column: (typeof columns)[number] }"
    >
      <template v-if="column.key === 'name'">
        <KbFileName :file-name="record.name" />
      </template>
    </template>
  </ATable>
</template>
