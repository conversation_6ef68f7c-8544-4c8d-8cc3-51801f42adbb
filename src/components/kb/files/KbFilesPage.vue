<script lang="ts">
  import type { TableItem as KbFile } from './table/KbFileTable.vue'

  const fakeFiles: KbFile[] = Array.from({ length: 20 }, (_, i) => ({
    id: `${i + 1}`,
    name: `文件 ${i + 1}`,
    chunkNum: Math.floor(Math.random() * 10) + 1,
    createTime: new Date(Date.now() - i * 1000 * 60 * 60),
    chunkMethod: 'text-splitter',
    chunkStatus: ['complete', 'running', 'failed', 'not-started'][i % 4] as KbFile['chunkStatus'],
  }))
</script>

<script setup lang="ts">
  import { kbFilesContextInjectionKey } from '@/utils/context/kb'

  import IconSearch from '~icons/ant-design/search-outlined'
  import IconPlay from '~icons/lucide/circle-play'
  import IconStop from '~icons/lucide/circle-stop'
  import IconTrash from '~icons/lucide/trash-2'

  const searchString = ref('')
  const searchStringDebounced = useDebounce(searchString, 200)

  const selectedKbs = ref<string[]>([])
  provide(kbFilesContextInjectionKey, {
    selectedKbs: shallowReadonly(selectedKbs),
    setSelectedKbs(value) {
      selectedKbs.value = value
    },
  })
</script>

<template>
  <div>
    <div class="flex flex-wrap items-center gap-y-4 px-(--page-px) pt-6">
      <AInput
        class="max-w-[300px] rounded-full!"
        placeholder="搜索模型名称..."
        v-model:value="searchString"
      >
        <template #suffix>
          <IconSearch class="text-foreground-4" />
        </template>
      </AInput>

      <div class="text-foreground-3 ml-4">
        共有
        <span class="text-primary">{{ fakeFiles.length }}</span>
        个筛选结果
      </div>

      <div class="ml-auto flex min-w-max items-center space-x-5">
        <AButton
          type="primary"
          class="gradient-a-button w-20!"
        >
          新建
        </AButton>

        <AButton
          type="primary"
          ghost
          class="outline-a-button flex! items-center gap-2"
          :disabled="selectedKbs.length === 0"
        >
          <IconPlay />
          解析
        </AButton>

        <AButton
          type="primary"
          ghost
          class="outline-a-button flex! items-center gap-2"
          :disabled="selectedKbs.length === 0"
        >
          <IconStop />
          停止
        </AButton>

        <AButton
          type="primary"
          ghost
          class="outline-a-button flex! items-center gap-2"
          :disabled="selectedKbs.length === 0"
        >
          <IconTrash />
          删除
        </AButton>
      </div>
    </div>

    <div class="px-(--page-px) py-4">
      <KbFileTable :files="fakeFiles" />
    </div>
  </div>
</template>
