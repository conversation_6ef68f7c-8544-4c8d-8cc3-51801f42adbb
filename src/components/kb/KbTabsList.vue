<script setup lang="ts">
  import { TabsList, TabsTrigger, injectTabsRootContext } from 'reka-ui'

  const { modelValue: activeTab } = injectTabsRootContext()
  const tabsListRef = useTemplateRef('tabsListRef')
  const activeTabRef = ref<HTMLElement>()

  const { width: containerWidth } = useElementSize(tabsListRef)

  const clipLeft = computed(() => {
    const activeElement = activeTabRef.value
    if (!activeElement) {
      return 0
    }
    return activeElement.offsetLeft
  })
  const clipRight = computed(() => {
    const activeElement = activeTabRef.value
    if (!activeElement) {
      return 0
    }
    return activeElement.offsetLeft + activeElement.offsetWidth
  })

  const tabs = [
    { label: '文件', value: 'files' },
    { label: '配置', value: 'settings' },
  ] as const

  function setActiveTabRef(value: string, el: HTMLElement) {
    if (value === activeTab.value) {
      activeTabRef.value = el
    }
  }
</script>

<template>
  <TabsList as-child>
    <div
      class="relative flex rounded-[50px] bg-[#3F8CFF26]"
      ref="tabsListRef"
    >
      <TabsTrigger
        v-for="tab in tabs"
        :key="tab.value"
        :value="tab.value"
        as-child
      >
        <button
          class="tabs-button"
          :ref="(el) => setActiveTabRef(tab.value, el as HTMLElement)"
        >
          {{ tab.label }}
        </button>
      </TabsTrigger>

      <div
        class="absolute inset-0 z-10 flex transition-[clip-path] duration-400 ease-out"
        :style="{
          'clip-path': `inset(0 ${Math.trunc(containerWidth - clipRight)}px 0 ${Math.trunc(clipLeft)}px round 50px)`,
        }"
      >
        <TabsTrigger
          v-for="tab in tabs"
          :key="tab.value"
          :value="tab.value"
          class="tabs-button overlay-button"
        >
          {{ tab.label }}
        </TabsTrigger>
      </div>
    </div>
  </TabsList>
</template>

<style scoped>
  .tabs-button {
    padding: 4px 16px;
    cursor: pointer;
    color: var(--color-primary);

    &.overlay-button {
      color: white;
      font-weight: bold;

      &:nth-child(odd) {
        background: linear-gradient(135deg, #219fffff 0%, #0066ffff 100%);
      }
      &:nth-child(even) {
        background: linear-gradient(45deg, #0066ffff 0%, #219fffff 100%);
      }
    }
  }
</style>
