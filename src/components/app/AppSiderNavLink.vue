<script setup lang="ts">
  import type { RouterLinkProps } from 'vue-router'
  import { twMerge } from 'tailwind-merge'
  import type { HTMLAttributes } from 'vue'

  import { injectAppSiderContext } from './AppSider.vue'

  defineOptions({ inheritAttrs: false })

  const { class: className, ...props } = defineProps<
    RouterLinkProps & {
      title: string
      icon: Component
      class?: HTMLAttributes['class']
    }
  >()

  const { isCollapsed } = injectAppSiderContext()
</script>

<template>
  <RouterLink
    class="block"
    v-bind="props"
    v-slot="{ isActive }"
  >
    <!-- @vue-expect-error hack to disable tooltip when not collapsed -->
    <ATooltip
      :trigger="isCollapsed ? 'hover' : 'never'"
      :title="props.title"
      placement="right"
    >
      <button
        :class="
          twMerge(
            'text-foreground-2 flex max-h-10 w-[138px] cursor-pointer items-center gap-2.5 overflow-hidden rounded-[30px] bg-[rgba(63,140,255,0.1)] py-2 pl-5 font-medium text-nowrap [&_svg]:shrink-0',
            isActive && 'bg-primary font-bold text-white',
            !isActive && 'hover:bg-[rgba(63,140,255,0.3)]',
            isCollapsed && 'size-8 max-h-8 rounded-[5px] pl-1',
            className,
          )
        "
        style="
          transition:
            all ease-in-out,
            border-radius ease-out,
            background-color ease-out,
            color ease-out;
        "
      >
        <props.icon />
        {{ props.title }}
      </button>
    </ATooltip>
  </RouterLink>
</template>
