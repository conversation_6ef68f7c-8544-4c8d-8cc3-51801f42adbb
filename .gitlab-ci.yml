stages:          
  - build
  # - test
  - deploy

variables:
  # registry.nscc-tj.cn 仓库账户和密码
  ci_registry: registry.nscc-tj.cn
  ci_registry_user: c3VuZng6aDAyejJSUkJWakY5T3M4Tw==

alpha-build:     
  stage: build
  image: registry.nscc-tj.cn/library/executor:v1.23.2-debug
  before_script:
    - unset http_proxy
    - unset https_proxy
    - unset no_proxy
    - echo "{\"auths\":{\"${ci_registry}\":{\"auth\":\"${ci_registry_user}\" }}}" > /kaniko/.docker/config.json
  script:
     - /kaniko/executor
      --context $CI_PROJECT_DIR
      --dockerfile Dockerfile
      --destination registry.nscc-tj.cn/aieducation/aieducation-frontend:alpha-$CI_COMMIT_SHORT_SHA
  rules:
     - if: '$CI_COMMIT_BRANCH == "develop"'
       when: always

# unit-test-job:   
#   stage: test    
#   script:
#     - echo "Running unit tests... This will take about 60 seconds."
#     - sleep 60
#     - echo "Code coverage is 90%"

# lint-test-job:   
#   stage: test    
#   script:
#       - npm ci
#       - npm run lint
#   allow_failure: true    

alpha-deploy:
  stage: deploy
  image: registry.nscc-tj.cn/k8s/kubectl:1.30.5
  script:
    - kubectl -n prod set image deployment/ai-education-frontend init=registry.nscc-tj.cn/aieducation/aieducation-frontend:alpha-${CI_COMMIT_SHORT_SHA}
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      when: always
