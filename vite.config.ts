import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import tailwindcss from '@tailwindcss/vite'
import icons from 'unplugin-icons/vite'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
import vueRouter from 'unplugin-vue-router/vite'
import { VueRouterAutoImports } from 'unplugin-vue-router'
import autoImport from 'unplugin-auto-import/vite'
import components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vueRouter(),
    vue(),
    vueDevTools(),
    icons({
      compiler: 'vue3',
      customCollections: {
        local: FileSystemIconLoader('./src/assets/icon'),
      },
      transform(svg) {
        // 匹配svg标签
        const svgTagMatch = svg.match(/<svg[^>]*>([\s\S]*)<\/svg>/)
        if (!svgTagMatch || !svgTagMatch[1]) {
          return svg
        }

        // 获取svg内容部分
        const svgContent = svgTagMatch[1]
        // 替换内容部分的fill和stroke属性
        const modifiedContent = svgContent
          .replace(/fill="[^"]*"/g, 'fill="currentColor"')
          .replace(/stroke="[^"]*"/g, 'stroke="currentColor"')

        // 重新组装svg
        return svg.replace(svgContent, modifiedContent)
      },
    }),
    tailwindcss(),
    components({
      resolvers: [AntDesignVueResolver({ prefix: 'A', importStyle: 'css-in-js' })],
    }),
    autoImport({
      dirs: ['./src/composables', './src/utils', './src/stores'],
      dirsScanOptions: {
        types: true,
      },
      imports: [
        VueRouterAutoImports,
        'vue',
        '@vueuse/core',
        'pinia',
        {
          '@tanstack/vue-query': ['useQuery', 'useMutation', 'useQueryClient', 'useInfiniteQuery'],
        },
      ],
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  css: {
    transformer: 'lightningcss', // 允许原生 css 规则嵌套
  },
  server: {
      host: "0.0.0.0",
      // port: +env.VITE_APP_PORT,
      open: true,
      proxy: {
        // 代理 /dev-api 的请求
        '/api': {
          target: 'http://************:8000',//'http://***************:9000',//env.VITE_APP_API_URL,//'http://*************:18000',//
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        }
      },
    },
})
